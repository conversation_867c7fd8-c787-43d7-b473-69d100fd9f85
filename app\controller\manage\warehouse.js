/**
 * @file 仓库管理控制器
 * @description 提供防护用品仓库管理的API接口
 */
const Controller = require('egg').Controller;

class WarehouseController extends Controller {
  /**
   * 获取仓库列表（不分页）
   */
  async getWarehouseList() {
    const { ctx } = this;

    try {
      const result = await ctx.service.warehouse.getWarehouseList();

      ctx.auditLog('查询仓库列表', '获取用户可访问的仓库列表', 'info');

      ctx.helper.renderSuccess(ctx, {
        data: result,
        message: '获取仓库列表成功',
      });
    } catch (error) {
      ctx.logger.error('获取仓库列表失败:', error);
      ctx.helper.renderFail(ctx, {
        message: error.message || '获取仓库列表失败',
      });
    }
  }

  /**
   * 获取仓库分页列表
   */
  async getWarehousePage() {
    const { ctx } = this;

    try {
      // 获取分页参数
      const { page = 1, pageSize = 10 } = ctx.query;
      const params = {
        page: parseInt(page),
        pageSize: parseInt(pageSize),
      };

      const result = await ctx.service.warehouse.getWarehousePage(params);

      ctx.auditLog('查询仓库分页列表', '获取用户可访问的仓库分页列表', 'info');

      ctx.helper.renderSuccess(ctx, {
        data: {
          list: result.data,
          pagination: {
            total: result.total,
            page: result.page,
            pageSize: result.pageSize,
            totalPages: result.totalPages,
          },
        },
        message: '获取仓库分页列表成功',
      });
    } catch (error) {
      ctx.logger.error('获取仓库分页列表失败:', error);
      ctx.helper.renderFail(ctx, {
        message: error.message || '获取仓库分页列表失败',
      });
    }
  }

  /**
   * 创建仓库
   */
  async createWarehouse() {
    const { ctx } = this;
    const data = ctx.request.body;

    try {
      // 参数验证
      if (!data.name || !data.name.trim()) {
        throw new Error('仓库名称不能为空');
      }

      if (data.name.length > 30) {
        throw new Error('仓库名称不能超过30个字符');
      }

      if (!data.managementScope || data.managementScope.length === 0) {
        throw new Error('请选择管理范围');
      }

      const result = await ctx.service.warehouse.createWarehouse(data);

      ctx.auditLog('创建仓库', `创建仓库: ${data.name}`, 'info');

      ctx.helper.renderSuccess(ctx, {
        data: result,
        message: '创建仓库成功',
      });
    } catch (error) {
      ctx.logger.error('创建仓库失败:', error);
      ctx.helper.renderFail(ctx, {
        message: error.message || '创建仓库失败',
      });
    }
  }

  /**
   * 更新仓库
   */
  async updateWarehouse() {
    const { ctx } = this;
    const data = ctx.request.body;

    try {
      // 参数验证
      if (!data._id) {
        throw new Error('仓库ID不能为空');
      }

      if (!data.name || !data.name.trim()) {
        throw new Error('仓库名称不能为空');
      }

      if (data.name.length > 30) {
        throw new Error('仓库名称不能超过30个字符');
      }

      if (!data.managementScope || data.managementScope.length === 0) {
        throw new Error('请选择管理范围');
      }

      const result = await ctx.service.warehouse.updateWarehouse(data);

      ctx.auditLog('更新仓库', `更新仓库: ${data.name}`, 'info');

      ctx.helper.renderSuccess(ctx, {
        data: result,
        message: '更新仓库成功',
      });
    } catch (error) {
      ctx.logger.error('更新仓库失败:', error);
      ctx.helper.renderFail(ctx, {
        message: error.message || '更新仓库失败',
      });
    }
  }

  /**
   * 删除仓库
   */
  async deleteWarehouse() {
    const { ctx } = this;
    const { warehouseId } = ctx.request.body;

    try {
      // 参数验证
      if (!warehouseId) {
        throw new Error('仓库ID不能为空');
      }

      const result = await ctx.service.warehouse.deleteWarehouse(warehouseId);

      ctx.auditLog('删除仓库', `删除仓库ID: ${warehouseId}`, 'info');

      ctx.helper.renderSuccess(ctx, {
        data: result,
        message: '删除仓库成功',
      });
    } catch (error) {
      ctx.logger.error('删除仓库失败:', error);
      ctx.helper.renderFail(ctx, {
        message: error.message || '删除仓库失败',
      });
    }
  }


  /**
   * 根据公司ID获取工作场所（懒加载）
   */
  async getWorkplacesByCompany() {
    const { ctx } = this;

    try {
      const companyId = ctx.query.companyId;
      const currentWarehouseId = ctx.query.currentWarehouseId || null;

      if (!companyId) {
        throw new Error('公司ID不能为空');
      }

      const result = await ctx.service.warehouse.getWorkplacesByCompany(companyId, currentWarehouseId);

      ctx.auditLog('查询工作场所', `获取公司${companyId}的工作场所`, 'info');

      ctx.helper.renderSuccess(ctx, {
        data: result,
        message: '获取工作场所成功',
      });
    } catch (error) {
      ctx.logger.error('获取工作场所失败:', error);
      ctx.helper.renderFail(ctx, {
        message: error.message || '获取工作场所失败',
      });
    }
  }

  /**
   * 获取公司列表
   */
  async getCompanyList() {
    const { ctx } = this;

    try {
      const currentWarehouseId = ctx.query.currentWarehouseId || null;
      const result = await ctx.service.warehouse.getCompanyList(currentWarehouseId);

      ctx.auditLog('查询公司列表', '获取用户权限范围内的公司列表', 'info');

      ctx.helper.renderSuccess(ctx, {
        data: result,
        message: '获取公司列表成功',
      });
    } catch (error) {
      ctx.logger.error('获取公司列表失败:', error);
      ctx.helper.renderFail(ctx, {
        message: error.message || '获取公司列表失败',
      });
    }
  }

  /**
   * 获取已被管理的fullId列表
   */
  async getManagedFullIds() {
    const { ctx } = this;

    try {
      const currentWarehouseId = ctx.query.currentWarehouseId || null;
      const result = await ctx.service.warehouse.getManagedFullIds(currentWarehouseId);

      ctx.auditLog('查询已管理范围', '获取已被其他仓库管理的范围', 'info');

      ctx.helper.renderSuccess(ctx, {
        data: Array.from(result),
        message: '获取已管理范围成功',
      });
    } catch (error) {
      ctx.logger.error('获取已管理范围失败:', error);
      ctx.helper.renderFail(ctx, {
        message: error.message || '获取已管理范围失败',
      });
    }
  }

  /**
   * 获取车间岗位树（保留兼容性）
   */
  async getMillConstructionTree() {
    const { ctx } = this;

    try {
      // 获取当前编辑的仓库ID（如果有的话）
      const currentWarehouseId = ctx.query.currentWarehouseId || null;
      // 获取企业ID参数
      const EnterpriseID = ctx.query.EnterpriseID || ctx.request.body.EnterpriseID || null;

      const result = await ctx.service.warehouse.getMillConstructionTree(currentWarehouseId, EnterpriseID);

      ctx.auditLog('查询车间岗位树', '获取车间岗位树结构', 'info');

      ctx.helper.renderSuccess(ctx, {
        data: result,
        message: '获取车间岗位树成功',
      });
    } catch (error) {
      ctx.logger.error('获取车间岗位树失败:', error);
      ctx.helper.renderFail(ctx, {
        message: error.message || '获取车间岗位树失败',
      });
    }
  }

  /**
   * 初始化公共仓库
   */
  async initPublicWarehouse() {
    const { ctx } = this;

    try {
      await ctx.service.warehouse.initPublicWarehouse();

      ctx.auditLog('初始化公共仓库', '创建默认公共仓库', 'info');

      ctx.helper.renderSuccess(ctx, {
        message: '初始化公共仓库成功',
      });
    } catch (error) {
      ctx.logger.error('初始化公共仓库失败:', error);
      ctx.helper.renderFail(ctx, {
        message: error.message || '初始化公共仓库失败',
      });
    }
  }
}

module.exports = WarehouseController;
