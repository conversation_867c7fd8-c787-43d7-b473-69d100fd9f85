/**
 * 工作区权限过滤器
 *
 * 统一基于 policy.js 中的 millConstruction_ids 进行权限过滤，支持不同的数据存储方式：
 *
 * 1. deviceCode 存储：数据表存储 deviceCode，通过 FlatMillConstructionMaterialized 视图的 unitCode 字段映射
 * 2. millConstructionId 存储：数据表直接存储 millConstruction 节点的 _id，通过 fullId 查找对应的节点 _id
 * 3. nodeId 存储：数据表存储节点的真实 _id，通过权限 fullId 查找对应的节点 _id 列表
 * 4. fullId 存储：数据表存储视图中的 fullId，可以直接进行层级权限匹配
 * 5. encode 存储：数据表存储部门ID等，通过 FlatMillConstructionMaterialized 视图的 encode 字段映射
 *
 * 所有模式最终都与 policy.js 中的 millConstruction_ids 格式保持一致
 *
 * ## 使用示例
 *
 * ### 1. deviceCode 模式（设备表）
 * ```javascript
 * const workspacePermissionFilter = require('../utils/workspacePermissionFilter');
 *
 * schema.plugin(workspacePermissionFilter.createWorkspacePermissionFilter(ctx, {
 *   filterField: 'deviceCode',    // 设备表中的设备编码字段
 *   storageType: 'deviceCode',    // 通过视图 unitCode 映射
 * }));
 * ```
 *
 * ### 2. millConstructionId 模式（存储权限fullId对应的节点ID）
 * ```javascript
 * schema.plugin(workspacePermissionFilter.createWorkspacePermissionFilter(ctx, {
 *   filterField: 'millConstructionId',  // 存储权限 fullId 对应的节点 ID
 *   storageType: 'millConstructionId',  // 通过 fullId 查找节点 ID
 * }));
 * ```
 *
 * ### 3. nodeId 模式（存储节点的真实_id）
 * ```javascript
 * schema.plugin(workspacePermissionFilter.createWorkspacePermissionFilter(ctx, {
 *   filterField: 'nodeId',             // 存储节点的真实 _id
 *   storageType: 'nodeId',             // 通过权限路径查找所有相关节点 _id
 * }));
 * ```
 *
 * ### 4. fullId 模式（通过视图 fullId 映射）
 * ```javascript
 * schema.plugin(workspacePermissionFilter.createWorkspacePermissionFilter(ctx, {
 *   filterField: 'departmentFullId',  // 存储视图中的 fullId
 *   storageType: 'fullId',           // 通过视图 fullId 映射
 * }));
 * ```
 *
 * ### 5. encode 模式（通过视图 encode 映射，如工伤管理）
 * ```javascript
 * schema.plugin(workspacePermissionFilter.createWorkspacePermissionFilter(ctx, {
 *   filterField: 'departmentId',  // 工伤表中的部门ID字段
 *   storageType: 'encode',        // 通过视图 encode 字段映射到 fullId 进行权限匹配
 * }));
 * ```
 *
 * ## 核心特性
 *
 * 1. **自动用户检测** - 无需手动传递用户ID，自动从多个来源获取
 * 2. **层级权限匹配** - 支持父级权限自动包含子级权限
 * 3. **高性能查询** - 使用 MongoDB 聚合管道和索引优化
 * 4. **透明集成** - 作为 Mongoose 插件自动生效，无需修改业务逻辑
 * 5. **递归检测** - 防止权限过滤器无限递归调用
 * 6. **多存储模式** - 支持不同的数据存储方式，统一权限逻辑
 *
 * <AUTHOR> Assistant
 * @version 1.0.0
 */

const RECURSION_KEY = Symbol('workspacePermissionFilter_recursion');


/**
 * 创建工作区权限过滤器
 *
 * @param {Object} ctx - Egg.js 上下文对象（通过 app.createAnonymousContext() 创建）
 * @param {Object} options - 配置选项
 * @param {string} options.filterField - 要过滤的字段名
 * @param {string} options.storageType - 存储类型：'deviceCode', 'millConstructionId', 'fullId', 'encode'
 * @returns {Function} Mongoose 插件函数
 */
function createWorkspacePermissionFilter(ctx, options = {}) {
  const { filterField, storageType } = options;

  if (!filterField) {
    throw new Error('filterField 参数是必需的');
  }

  if (![ 'deviceCode', 'millConstructionId', 'nodeId', 'fullId', 'encode' ].includes(storageType)) {
    throw new Error('storageType 必须是 deviceCode, millConstructionId, nodeId, fullId 或 encode 之一');
  }

  return function workspacePermissionFilterPlugin(schema) {
    // 在所有查询操作前添加权限过滤
    const queryMethods = [
      'find', 'findOne', 'findOneAndUpdate', 'findOneAndDelete',
      'updateOne', 'updateMany', 'deleteOne', 'deleteMany', 'countDocuments',
    ];

    queryMethods.forEach(method => {
      schema.pre(method, async function() {
        // 检查是否已经在权限过滤递归中，避免无限递归
        if (this.getOptions()[RECURSION_KEY]) {
          return;
        }

        // 检查是否明确禁用权限检查
        if (this.getOptions().authCheck === false) {
          return;
        }

        try {
          // 标记当前查询正在进行权限过滤，防止递归
          this.setOptions({ [RECURSION_KEY]: true });

          // 从查询选项中获取用户信息
          const queryOptions = this.getOptions();
          const { userid = '', authCheck = true } = queryOptions;

          if (!userid || !authCheck) {
            return;
          }

          // 获取权限过滤条件
          const permissionFilter = await getPermissionFilter(ctx, { ...options, userid });

          if (permissionFilter && Object.keys(permissionFilter).length > 0) {
            // 合并权限过滤条件到当前查询
            const currentQuery = this.getQuery();
            const mergedQuery = {
              $and: [
                currentQuery,
                permissionFilter,
              ],
            };
            this.setQuery(mergedQuery);
          }
        } catch (error) {
          ctx.logger.error('权限过滤失败:', error);
          // 权限过滤失败时，为了安全起见，返回空结果
          this.setQuery({ _id: { $in: [] } });
        }
      });
    });

    // 聚合查询的权限过滤
    schema.pre('aggregate', async function() {
      // 检查是否已经在权限过滤递归中
      if (this.options[RECURSION_KEY]) {
        return;
      }

      // 检查是否明确禁用权限检查
      if (this.options.authCheck === false) {
        return;
      }

      try {
        // 标记当前聚合正在进行权限过滤
        this.options[RECURSION_KEY] = true;

        // 从查询选项中获取用户信息
        const { userid = '', authCheck = true } = this.options;

        ctx.logger.info('聚合权限过滤 - 选项信息:', {
          userid,
          authCheck,
          storageType: options.storageType,
          filterField: options.filterField,
        });

        if (!userid || !authCheck) {
          ctx.logger.info('聚合权限过滤 - 跳过:', { userid, authCheck });
          return;
        }

        // 获取权限过滤条件
        const permissionFilter = await getPermissionFilter(ctx, { ...options, userid });

        if (permissionFilter && Object.keys(permissionFilter).length > 0) {
          ctx.logger.info('聚合权限过滤 - 应用过滤条件:', permissionFilter);
          // 在聚合管道开始处添加权限过滤
          this.pipeline().unshift({ $match: permissionFilter });
        } else {
          ctx.logger.info('聚合权限过滤 - 无过滤条件');
        }
      } catch (error) {
        ctx.logger.error('聚合权限过滤失败:', error);
        // 权限过滤失败时，为了安全起见，返回空结果
        this.pipeline().unshift({ $match: { _id: { $in: [] } } });
      }
    });
  };
}

/**
 * 获取权限过滤条件
 *
 * @param {Object} ctx - Egg.js 上下文对象
 * @param {Object} options - 配置选项
 * @returns {Promise<Object>} 权限过滤条件
 */
async function getPermissionFilter(ctx, options) {
  const { filterField, storageType, userid } = options;

  ctx.logger.info('getPermissionFilter 开始执行:', {
    filterField,
    storageType,
    userid,
    hasCtx: !!ctx,
    hasHelper: !!(ctx && ctx.helper),
  });

  if (!userid) {
    ctx.logger.warn('无法获取用户ID，跳过权限过滤');
    return {};
  }

  try {
    // 检查是否为超级管理员
    const isSuperAdmin = await ctx.helper.getScopeData('superAdmin', userid);
    if (
      isSuperAdmin === '1' &&
      ctx.app.config.permission.deploymentMode === 'single-tenant'
    ) {
      ctx.logger.info('用户为超级管理员，跳过权限过滤');
      return {}; // 返回空对象，表示不添加任何过滤条件
    }

    let allowedValues = [];

    if (storageType === 'deviceCode') {
      // 通过视图 unitCode 字段映射获取允许的设备代码
      const millConstructionIds = await getMillConstructionIds(userid, ctx);
      allowedValues = await getAllowedDeviceCodes(millConstructionIds, ctx);
    } else if (storageType === 'millConstructionId') {
      // 直接使用 millConstruction IDs（fullId 格式）
      allowedValues = await getMillConstructionIds(userid, ctx);
    } else if (storageType === 'nodeId') {
      // 通过权限 fullId 查找对应的节点 _id 列表
      const millConstructionIds = await getMillConstructionIds(userid, ctx);
      allowedValues = await getAllowedNodeIds(millConstructionIds, ctx);
    } else if (storageType === 'fullId') {
      // 通过视图 fullId 字段映射获取允许的值
      const millConstructionIds = await getMillConstructionIds(userid, ctx);
      allowedValues = await getAllowedFullIds(millConstructionIds, ctx);
    } else if (storageType === 'encode') {
      // 通过视图 encode 字段映射获取允许的值
      const millConstructionIds = await getMillConstructionIds(userid, ctx);
      allowedValues = await getAllowedEncodeValues(millConstructionIds, ctx);
    }

    if (!allowedValues || allowedValues.length === 0) {
      ctx.logger.warn(`用户 ${userid} 没有任何 ${storageType} 权限`);
      return { [filterField]: { $in: [] } }; // 返回空结果
    }

    return { [filterField]: { $in: allowedValues } };

  } catch (error) {
    ctx.logger.error('获取权限过滤条件失败:', error);
    return { [filterField]: { $in: [] } }; // 出错时返回空结果，确保安全
  }
}

/**
 * 获取用户的 millConstruction IDs
 *
 * @param {string} userid - 用户ID
 * @param {Object} ctx - Egg.js 上下文对象
 * @returns {Promise<Array>} millConstruction IDs 数组
 */
async function getMillConstructionIds(userid, ctx) {
  try {
    ctx.logger.info('getMillConstructionIds 开始执行:', { userid });

    if (!userid) {
      ctx.logger.warn('无法获取用户ID');
      return [];
    }

    // 注意：超级管理员检查已在 getPermissionFilter 中处理，这里不再需要检查

    // 使用 ctx.helper.getScopeData 获取权限数据（与现有权限系统保持一致）
    ctx.logger.info('获取用户 millConstruction 权限:', { userid });
    const millConstructionIds = await ctx.helper.getScopeData('millConstruction_ids', userid);
    ctx.logger.info('获取到的 millConstruction IDs:', {
      userid,
      millConstructionIds,
      isArray: Array.isArray(millConstructionIds),
      length: millConstructionIds ? millConstructionIds.length : 0,
    });

    if (!Array.isArray(millConstructionIds)) {
      ctx.logger.warn(`用户 ${userid} 的 millConstruction_ids 不是数组:`, millConstructionIds);
      return [];
    }

    return millConstructionIds;
  } catch (error) {
    ctx.logger.error('获取用户权限失败:', error);
    return [];
  }
}

/**
 * 根据 millConstruction IDs 获取允许的设备代码
 *
 * @param {Array} millConstructionIds - millConstruction IDs 数组
 * @param {Object} ctx - Egg.js 上下文对象
 * @returns {Promise<Array>} 允许的设备代码数组
 */
async function getAllowedDeviceCodes(millConstructionIds, ctx) {
  try {
    // 构建层级正则表达式
    const hierarchyRegex = buildHierarchyRegex(millConstructionIds);

    // 从视图中查询匹配的设备代码
    const viewDocs = await ctx.model.FlatMillConstructionMaterialized
      .find({
        fullId: { $regex: hierarchyRegex },
      }, 'unitCode', { authCheck: false })
      .lean()
      .exec();

    // 提取设备代码并去重
    const deviceCodes = [ ...new Set(viewDocs.map(doc => doc.unitCode).filter(Boolean)) ];

    ctx.logger.info(`根据权限 ${millConstructionIds} 查询到 ${deviceCodes.length} 个允许的设备代码`);
    return deviceCodes;

  } catch (error) {
    ctx.logger.error('查询允许的设备代码失败:', error);
    return [];
  }
}

/**
 * 根据 millConstruction IDs 获取允许的节点 _id 列表
 *
 * @param {Array} millConstructionIds - millConstruction IDs 数组（fullId 格式）
 * @param {Object} ctx - Egg.js 上下文对象
 * @returns {Promise<Array>} 允许的节点 _id 数组
 */
async function getAllowedNodeIds(millConstructionIds, ctx) {
  try {
    ctx.logger.info('getAllowedNodeIds 开始执行:', {
      millConstructionIds,
      length: millConstructionIds ? millConstructionIds.length : 0,
    });

    // 构建层级正则表达式
    const hierarchyRegex = buildHierarchyRegex(millConstructionIds);
    ctx.logger.info('构建的层级正则表达式:', { hierarchyRegex: hierarchyRegex.toString() });

    // 从视图中查询匹配的节点 _id
    ctx.logger.info('开始查询 FlatMillConstructionMaterialized 视图...');

    // 分别查询：1. 权限节点及其子级  2. 权限节点的父级
    const allNodeIds = new Set();

    for (const permissionFullId of millConstructionIds) {
      // 1. 查询权限节点本身
      const exactMatch = await ctx.model.FlatMillConstructionMaterialized
        .findOne({
          fullId: permissionFullId,
        }, '_id', { authCheck: false })
        .lean()
        .exec();

      if (exactMatch) {
        allNodeIds.add(exactMatch._id);
        ctx.logger.info('找到权限节点:', { fullId: permissionFullId, nodeId: exactMatch._id });
      }

      // 2. 查询权限节点的所有子级
      const childPattern = new RegExp(`^${permissionFullId.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}_child_`);
      const childNodes = await ctx.model.FlatMillConstructionMaterialized
        .find({
          fullId: { $regex: childPattern },
        }, '_id fullId', { authCheck: false })
        .lean()
        .exec();

      childNodes.forEach(child => {
        allNodeIds.add(child._id);
        ctx.logger.info('找到子级节点:', { fullId: child.fullId, nodeId: child._id });
      });

      // 3. 查询权限节点的父级路径
      const parentPatterns = extractParentPatterns(permissionFullId);
      for (const pattern of parentPatterns) {
        const parentRegex = new RegExp(pattern);
        const parentNodes = await ctx.model.FlatMillConstructionMaterialized
          .find({
            fullId: { $regex: parentRegex },
          }, '_id fullId', { authCheck: false })
          .lean()
          .exec();

        parentNodes.forEach(parent => {
          allNodeIds.add(parent._id);
          ctx.logger.info('找到父级节点:', { fullId: parent.fullId, nodeId: parent._id });
        });
      }
    }

    const viewDocs = Array.from(allNodeIds).map(id => ({ _id: id }));

    ctx.logger.info('视图查询结果:', {
      docCount: viewDocs ? viewDocs.length : 0,
      sampleDocs: viewDocs ? viewDocs.slice(0, 3) : [],
    });

    // 提取节点 _id（已经去重）
    const nodeIds = viewDocs.map(doc => doc._id).filter(Boolean);

    ctx.logger.info(`根据权限 ${millConstructionIds} 查询到 ${nodeIds.length} 个允许的节点 _id`);
    return nodeIds;

  } catch (error) {
    ctx.logger.error('查询允许的节点 _id 失败:', error);
    return [];
  }
}

/**
 * 根据 millConstruction IDs 获取允许的 fullId 值
 *
 * @param {Array} millConstructionIds - millConstruction IDs 数组
 * @param {Object} ctx - Egg.js 上下文对象
 * @returns {Promise<Array>} 允许的 fullId 值数组
 */
async function getAllowedFullIds(millConstructionIds, ctx) {
  try {
    // 构建层级正则表达式
    const hierarchyRegex = buildHierarchyRegex(millConstructionIds);

    // 从视图中查询匹配的 fullId 值
    const viewDocs = await ctx.model.FlatMillConstructionMaterialized
      .find({
        fullId: { $regex: hierarchyRegex },
      }, 'fullId', { authCheck: false })
      .lean()
      .exec();

    // 提取 fullId 值并去重
    const fullIds = [ ...new Set(viewDocs.map(doc => doc.fullId).filter(Boolean)) ];

    ctx.logger.info(`根据权限 ${millConstructionIds} 查询到 ${fullIds.length} 个允许的 fullId`);
    return fullIds;

  } catch (error) {
    ctx.logger.error('查询允许的 fullId 值失败:', error);
    return [];
  }
}

/**
 * 根据 millConstruction IDs 获取允许的 encode 值
 *
 * @param {Array} millConstructionIds - millConstruction IDs 数组
 * @param {Object} ctx - Egg.js 上下文对象
 * @returns {Promise<Array>} 允许的 encode 值数组
 */
async function getAllowedEncodeValues(millConstructionIds, ctx) {
  try {
    ctx.logger.info('getAllowedEncodeValues 开始执行:', {
      millConstructionIds,
      length: millConstructionIds ? millConstructionIds.length : 0,
    });

    // 构建层级正则表达式
    const hierarchyRegex = buildHierarchyRegex(millConstructionIds);
    ctx.logger.info('构建的层级正则表达式:', { hierarchyRegex: hierarchyRegex.toString() });

    // 从视图中查询匹配的 encode 值
    ctx.logger.info('开始查询 FlatMillConstructionMaterialized 视图...');
    const viewDocs = await ctx.model.FlatMillConstructionMaterialized
      .find({
        fullId: { $regex: hierarchyRegex },
      }, 'encode', { authCheck: false })
      .lean()
      .exec();

    ctx.logger.info('视图查询结果:', {
      docCount: viewDocs ? viewDocs.length : 0,
      sampleDocs: viewDocs ? viewDocs.slice(0, 3) : [],
    });

    // 提取 encode 值并去重
    const encodeValues = [ ...new Set(viewDocs.map(doc => doc.encode).filter(Boolean)) ];

    ctx.logger.info(`根据权限 ${millConstructionIds} 查询到 ${encodeValues.length} 个允许的 encode`);
    return encodeValues;

  } catch (error) {
    ctx.logger.error('查询允许的 encode 值失败:', error);
    return [];
  }
}

/**
 * 构建层级正则表达式
 * @param {Array} millConstructionIds - millConstruction IDs 数组
 * @returns {RegExp} 层级正则表达式
 */
function buildHierarchyRegex(millConstructionIds) {
  if (!Array.isArray(millConstructionIds) || millConstructionIds.length === 0) {
    return /^$/; // 匹配空字符串，即不匹配任何内容
  }

  const allPatterns = [];

  millConstructionIds.forEach(id => {
    // 转义特殊字符
    const escapedId = id.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');

    // 1. 匹配权限节点本身及其所有子级
    allPatterns.push(`^${escapedId}(/.*)?$`);

    // 2. 解析并匹配权限节点的所有父级节点
    const parentPatterns = extractParentPatterns(id);
    allPatterns.push(...parentPatterns);
  });

  // 去重并使用 OR 操作符组合所有模式
  const uniquePatterns = [ ...new Set(allPatterns) ];
  return new RegExp(uniquePatterns.join('|'));
}

/**
 * 从 fullId 中提取所有父级节点的匹配模式
 * @param {string} fullId - 完整的层级ID
 * @returns {Array} 父级节点的正则模式数组
 */
function extractParentPatterns(fullId) {
  const patterns = [];

  // 解析 fullId 的层级结构
  // 例如: 3_Kgb2cqy_child_workspaces_aBC4UzcZX
  const parts = fullId.split('_child_');

  if (parts.length > 1) {
    // 构建父级路径
    let currentPath = parts[0]; // 3_Kgb2cqy

    // 转义特殊字符并添加父级节点模式
    const escapedPath = currentPath.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
    patterns.push(`^${escapedPath}$`);

    // 如果有更多层级，继续构建中间层级的模式
    for (let i = 1; i < parts.length - 1; i++) {
      currentPath += '_child_' + parts[i];
      const escapedCurrentPath = currentPath.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
      patterns.push(`^${escapedCurrentPath}$`);
    }
  }

  return patterns;
}

module.exports = {
  createWorkspacePermissionFilter,
};
