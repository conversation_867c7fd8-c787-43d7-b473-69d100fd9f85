<template>
  <div class="millConstruction" @click="showRightMenu = false">
    <div class="millLeft">
      <div class="addMill">
        <el-tooltip class="item" effect="dark" content="添加" placement="bottom">
          <el-button
            @click="openAddMill"
            size="mini"
            type="primary"
            >{{ '添加'+mill+'/'+workspaces }}</el-button
          >
        </el-tooltip>
        <el-button type="danger" size="mini" @click="deleteNode" :disabled="hascheck"
          >删除</el-button
        >
        <el-button size="small" plain type="primary" @click="gotoExcelImport"
          >批量导入</el-button
        >
        <json-excel type="xls" class="json2excel" :data="excelMills" name="工作场所">
        </json-excel>
        <el-button size="mini" @click="jsonToExcel">导出excel</el-button>
        <el-button v-if="branch!=='wh'" size="mini" @click="updateDepartsLink">同步部门绑定</el-button>
        <el-badge :value="byNum" :max="99" class="byReview" v-if="branch === 'by' || branch === 'wh'">
          <el-button class="share-button" icon="el-icon-user-solid" type="primary" size="mini" @click="gotoByReview"></el-button>
        </el-badge>
        <el-input clearable size="mini" v-model="filterText" placeholder="请输入内容">
          <i class="el-icon-search el-input__icon" slot="suffix"></i>
        </el-input>
        <el-button size="small" @click="gotoHarmFactor" v-if="false"
          >导入危害因素</el-button
        >
      </div>
      <el-checkbox v-model="checkAll" @change="handleCheckAllChange">全选</el-checkbox>
      <p></p>
      <el-tree
        :expand-on-click-node="false"
        @check="oncheck"
        :check-strictly="true"
        @node-click="clickleft"
        @node-expand="onNodeExpand"
        @node-collapse="onNodeCollapse"
        @node-contextmenu="clickright"
        @node-drop="sort"
        :allow-drop="allowDrop"
        :allow-drag="allowDrag"
        ref="tree"
        :filter-node-method="filterNode"
        lazy
        :load="loadLazyData"
        :default-expanded-keys="expandedIds"
        node-key="_id"
        :props="defaultProps"
        :key="treeKey"
        show-checkbox
      >
        <div
          class="custom-tree-node"
          :class="{
            showCheck: employeeMoveId === (node.parent && node.parent.data && node.parent.data._id),
            displayCheck:
              employeeMoveId !== (node.parent && node.parent.data && node.parent.data._id) &&
              node.parent && node.parent.data && node.parent.data.category === 'stations',
            displayNode:
              !node.label || (node.parent && node.parent.data && node.parent.data.category === 'stations' &&
                  node.parent.data.children && node.parent.data.children.findIndex((d) => d._id === data._id) >=
                    (showEmployeeCount[node.parent.data._id] || initCount))
                  ? true
                  : false,
          }"
          slot-scope="{ node, data }"
        >
          <span v-if="activeId !== node.id">
            <span v-show="!(node.parent && node.parent.data && node.parent.data.category === 'stations')" class="addEmployee"
              >{{ node.label }}({{ node.data.totalEmployeeCount || 0 }}人)
              <span v-show="node.data.status === '0'">
                <span :style="{ color: 'red' }">(待确认)</span>
              </span>
            </span>
            <span v-show="node.parent && node.parent.data && node.parent.data.category === 'stations'">
              <el-link @click="employeeId = node.data._id">{{
                node.label
              }}
              <span>{{ node.data.unitCode }}</span>
            </el-link>
            </span>
            <span
              size="mini"
              v-show="
                node.parent && node.parent.data && node.parent.data.category === 'stations' &&
                node.data.category === 'employee' && node.data.status === 0
              "
            >
              <span :style="{ color: 'red' }">(已离职)</span>
            </span>
          </span>
          <el-input
            @keyup.enter="handleEdit({}, node)"
            ref="editInput"
            @blur="activeId = ''"
            @change="onchange(node)"
            v-if="activeId === node.id"
            size="mini"
            v-model="editText"
          ></el-input>
          <div class="rigthOperation" v-if="node.data.category !== 'enterprises'">
            <div v-if="node.data.status === '0'">
              <el-tooltip
                  class="item"
                  v-show="!(node.parent && node.parent.data && node.parent.data.category === 'stations')"
                  effect="dark"
                  clearable
                  :content="'检测机构添加的'+workspaces+'和'+stations+'，是否确认'"
                  placement="bottom"
                >
                <el-link
                  icon="el-icon-circle-check"
                  @click.stop="handleEdit({ status: '1' }, node)"
                ></el-link>
              </el-tooltip>
              <el-tooltip class="item" effect="dark" content="删除" placement="bottom">
                <el-link
                  icon="el-icon-delete"
                  @click.stop="handleDelete(node, data)"
                ></el-link>
              </el-tooltip>
            </div>

            <div v-else>
              <el-tooltip class="item" effect="dark" content="编码" placement="bottom" v-if="branch === 'wh' && !(node.parent && node.parent.data && node.parent.data.category === 'stations')">
                <el-link icon="el-icon-document-copy" @click.stop="editMillUnitCode(node)"></el-link>
              </el-tooltip>
              <el-tooltip
                  class="item"
                  v-show="!(node.parent && node.parent.data && node.parent.data.category === 'stations')"
                  effect="dark"
                  clearable
                  content="修改"
                  placement="bottom"
                >
                <el-link icon="el-icon-edit" @click.stop="edit(node)"></el-link>
              </el-tooltip>

              <el-tooltip
                  v-show="!(node.parent && node.parent.data && node.parent.data.category === 'stations')"
                  class="item"
                  effect="dark"
                  content="添加"
                  placement="bottom"
                >
                  <el-link
                    v-if="node.data.category === 'stations'"
                    @click.stop="openDialog(node)"
                  >
                    <img src="@/assets/addEmployee.png" />
                  </el-link>
                  <el-link
                    v-if="node.data.category !== 'stations'"
                    icon="el-icon-circle-plus-outline"
                    @click.stop="openDialog(node)"
                  ></el-link>
              </el-tooltip>

              <el-tooltip class="item" effect="dark" content="删除" placement="bottom">
                <el-link
                  icon="el-icon-delete"
                  @click.stop="handleDelete(node, data)"
                ></el-link>
              </el-tooltip>
              <el-tooltip
                  v-show="node.data.category === 'stations'"
                  class="item"
                  effect="dark"
                  :content="'修改'+stations+'信息'"
                  placement="bottom"
                >
                <el-link
                  icon="el-icon-info"
                  @click.stop="
                    dialogFormVisible = true;
                    activeNode = node;
                  "
                ></el-link>
              </el-tooltip>
              <el-tooltip
                  v-show="node.parent && node.parent.data && node.parent.data.category === 'stations'"
                  class="item"
                  effect="dark"
                  content="查看人员信息"
                  placement="bottom"
                >
                <el-link
                  icon="el-icon-info"
                  @click="employeeId = node.data._id"
                ></el-link>
              </el-tooltip>
              <!-- 等待员工确认转岗 -->
                <el-button
                  type="warning"
                  plain
                  size="mini"
                  v-show="
                    node.parent && node.parent.data && node.parent.data.category === 'stations' && node.data.isPass === '1'
                  "
                  >等待确认</el-button
                >
              <el-tooltip
                  v-show="node.data.category === 'stations' && branch !== 'wh'"
                  class="item"
                  effect="dark"
                  content="人员批量转岗"
                  placement="bottom"
                >
                <el-link @click.stop="employeeMove(node)">
                  <img src="@/assets/move.png" />
                </el-link>
              </el-tooltip>
              <el-button
                size="mini"
                v-show="node.data._id === employeeMoveId"
                @click.stop="openMoveDialog"
                :disabled="hascheck"
                type="success"
                >转岗</el-button
              >
              <el-button
                size="mini"
                v-show="node.data._id === employeeMoveId"
                @click.stop="cancalMove"
                >取消</el-button
              >
            </div>
          </div>
          <el-button
            @click.stop="showNode(node)"
            type="primary"
            plain
            size="mini"
            v-if="
              (showOpen[node.parent && node.parent.data && node.parent.data._id] || true) &&
              node.parent && node.parent.data && node.parent.data.category === 'stations' &&
              node.parent.data.children && node.parent.data.children.findIndex((d) => d._id === data._id) ===
                (showEmployeeCount[node.parent.data._id] || initCount) - 1 &&
              node.parent.data.children.length -
                (showEmployeeCount[node.parent.data._id] || initCount) > 0
            "
            >展开剩余{{
              node.parent.data.children.length -
              (showEmployeeCount[node.parent.data._id] || initCount)
            }}名人员<i class="el-icon-arrow-down el-icon--right"></i
          ></el-button>
          <el-button
            @click.stop="hideNode(node)"
            v-if="
              (!showOpen[node.parent && node.parent.data && node.parent.data._id] || false) &&
              node.parent && node.parent.data && node.parent.data.children &&
              node.parent.data.children.length - (showEmployeeCount[node.parent.data._id] || initCount) === 0 &&
              node.parent.data.category === 'stations' &&
              node.parent.data.children && node.parent.data.children.findIndex((d) => d._id === data._id) ===
                (showEmployeeCount[node.parent.data._id] || initCount) - 1
            "
            type="primary"
            size="mini"
            plain
            >收起<i class="el-icon-arrow-up el-icon--right"></i
          ></el-button>
          <!-- 暂时先不实现 -->
          <!-- <div
            :style="{ ...rightMenu }"
            class="positionCard"
            v-show="showRightMenu && activeNode.id === node.id"
          >
            <div @click="copyclick">
              <span>复制</span>
              <span class="el-icon-document-copy"></span>
            </div>
            <div @click="handlePaste">
              <span>粘贴</span>
              <span class="el-icon-tickets"></span>
            </div>
            <div>
              <span>移动</span>
              <span class="el-icon-tickets"></span>
            </div>
          </div> -->
        </div>
      </el-tree>
      <el-dialog :title="addPlant" :visible.sync="dialogVisible" width="40%">
        <el-form
          label-position="right"
          ref="nameForm"
          :model="nameForm"
          :inline="true"
          :rules="rules"
          label-width="80px"
        >
          <el-form-item label="企业" prop="EnterpriseID" v-show="(title === '添加厂房/装置' || title === '添加厂房/车间') && isMultiEnterprise">
            <el-select v-model="nameForm.EnterpriseID" placeholder="请选择企业">
              <el-option v-for="item in enterprises" :key="item._id" :label="item.cname" :value="item._id"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item :label="label" v-show="activeNode && activeNode.label">
            <el-input
              @keyup.enter="addMill"
              v-show="title !== '添加厂房/车间' || title !== '添加装置/工序'"
              :value="activeNode && activeNode.label"
              :style="{ width: '200px' }"
              disabled
            ></el-input>
          </el-form-item>
          <div class="selectEmployee">
            <el-form-item prop="name" :label="nameLabel">
              <el-input
                @keyup.enter="addMill"
                v-show="title !== '添加员工'"
                clearable
                v-model="nameForm.name"
                placeholder="请输入名称"
                :style="{ width: '200px' }"
              ></el-input>
              <el-select
                clearable
                @remove-tag="onRemoveTag"
                @change="onSelectChange"
                :style="{ width: '350px' }"
                multiple
                v-show="title === '添加员工'"
                v-model="nameForm.name"
                filterable
                remote
                :remote-method="remoteMethod"
                reserve-keyword
                placeholder="请输入关键词"
                :loading="loading"
              >
                <el-option
                  v-for="item in employees"
                  :key="item._id"
                  :label="item.unitCode?item.name+'（'+item.unitCode+'）':item.name"
                  :value="item._id"
                ></el-option>
              </el-select>
              <div class="el-form-item__error">{{ repeatError }}</div>
            </el-form-item>
          </div>
          <el-form-item label="类型" prop="category" v-if="!activeNode || !activeNode.level">
            <el-radio-group v-model="nameForm.category">
              <el-radio label="mill">{{ mill }}</el-radio>
              <el-radio label="workspaces">{{ workspaces }}</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-form>
        <span slot="footer" class="dialog-footer">
          <el-button @click="dialogVisible = false" size="small">取 消</el-button>
          <el-button type="primary" @click="addMill" size="small">确 定</el-button>
        </span>
      </el-dialog>
      <el-dialog :title="stations+'信息输入'" :visible.sync="dialogFormVisible">
        <station-form
          :ruleForm="ruleForm"
          :stationName="activeNode && activeNode.label"
          :activeNode="activeNode"
          @findMillConstruction="findMillConstruction"
        ></station-form>
        <div slot="footer" class="dialog-footer">
          <el-button
            @click="
              dialogFormVisible = false;
              $refs.ruleForm.resetFields();
            "
            >取 消</el-button
          >
          <el-button type="primary" @click="upload()">确 定</el-button>
        </div>
      </el-dialog>
      <el-dialog
        title="人员转岗"
        :visible.sync="moveEmployeesDialog"
        width="386px"
        :before-close="handleClose"
      >
        <move-employee
          :key="moveEmployeeKey"
          @getSelectIds="getSelectIds"
          @getTime="getTime"
          @getReason="getReason"
          @getFiles="getFiles"
          :selectStationIds="selectStationIds"
          :EnterpriseID="activeNode.data&&activeNode.data.EnterpriseID"
        ></move-employee>
        <span slot="footer" class="dialog-footer">
          <el-button
            @click.stop="
              moveEmployeesDialog = false;
              cancalMove();
            "
            >取 消</el-button
          >
          <el-button type="primary" @click.stop="saveMoveEmployee">确 定</el-button>
        </span>
      </el-dialog>
      
      <!-- 编码对话框 -->
      <el-dialog title="编辑编码" :visible.sync="unitCodeDialogVisible" width="30%">
        <el-form :model="unitCodeForm" label-width="80px">
          <el-form-item label="名称">
            <el-input v-model="unitCodeForm.name" disabled></el-input>
          </el-form-item>
          <el-form-item label="编码">
            <el-input v-model="unitCodeForm.unitCode" placeholder="请输入编码"></el-input>
          </el-form-item>
          <el-form-item label="工伤编码">
            <el-input v-model="unitCodeForm.encode" placeholder="请输入编码"></el-input>
          </el-form-item>
        </el-form>
        <span slot="footer" class="dialog-footer">
          <el-button @click="unitCodeDialogVisible = false">取 消</el-button>
          <el-button type="primary" @click="saveUnitCode">确 定</el-button>
        </span>
      </el-dialog>
      
    </div>
    <div class="middleLine"></div>
    <div class="rightInfo">
      <div class="statisticInfo" v-if="!hascheck">
        <statistic-info :data="$refs.tree.getCheckedNodes()" :isGroup="isMultiEnterprise"
        v-if="$refs.tree.getCheckedNodes().length > 0 && $refs.tree.getCheckedNodes()[0].category && $refs.tree.getCheckedNodes()[0].category != 'employees'"
        >
        </statistic-info>
      </div>
      <div
        class="stationInfo"
        v-if="
          hascheck &&
          activeNode.data &&
          activeNode.data.category === 'stations' &&
          ruleForm._id
        "
      >
        <el-tabs
          type="border-card"
          style="width: 100%"
          v-model="activeName"
          @tab-click="handleClick"
        >
          <el-tab-pane :label="stations+'信息'" :name="stations+'信息'">
            <el-card class="box-card">
              <div slot="header" class="clearfix">
                <span>{{stations}}信息</span>
              </div>
              <station-form
                :harmFactors="harmFactors"
                :ruleForm="ruleForm"
                :stationName="activeNode && activeNode.label"
                :activeNode="activeNode"
                @findMillConstruction="findMillConstruction"
              ></station-form>
              <div style="display: flex; justify-content: center">
                <el-button type="primary" @click="upload()">保存</el-button>
              </div>
            </el-card>
          </el-tab-pane>
          <el-tab-pane label="危害因素检测结果" name="危害因素检测结果">
            <el-card class="box-card">
              <div slot="header" class="clearfix">
                <span>危害因素检测结果</span>
                <el-tooltip class="item" effect="dark" content="全屏" placement="top">
                  <el-button
                    @click="dialogCheckResult = true"
                    style="float: right; padding: 3px 0"
                    type="text"
                    icon="el-icon-full-screen"
                  ></el-button>
                </el-tooltip>
              </div>
              <check-result ref="checkResult"></check-result>
            </el-card>
          </el-tab-pane>
          <el-tab-pane v-for="item in materialList" :label="item.table" :name="item._id">
            <el-card class="box-card">
              <div slot="header" class="clearfix">
                <span>{{ item.table }}</span>
              </div>
              <materialInfo :materialList="item" />
            </el-card>
          </el-tab-pane>
        </el-tabs>
      </div>
      <employee-info
        ref="employeeInfo"
        v-show="
          activeNode.data && activeNode.parent && activeNode.parent.data && activeNode.parent.data.category === 'stations' && employeeId
        "
        :_id="employeeId"
        :enterpriseId="activeNode && activeNode.data && activeNode.data.EnterpriseID"
        :branch="branch"
      ></employee-info>
    </div>
    <el-dialog title="危害检测结果" :visible.sync="dialogCheckResult" width="80%" center>
      <check-result ref="checkResult"></check-result>
    </el-dialog>
  </div>
</template>

<script>
import statisticInfo from "@/components/subCom/statisticInfo.vue";
import checkResult from "@/components/subCom/checkResult.vue";
import stationForm from "@/components/subCom/stationForm";
import moveEmployee from "@/components/moveEmployee.vue";
import employeeInfo from "@/components/subCom/employeeInfo.vue";
import materialInfo from "@/components/subCom/materialInfo.vue";
import { adminResourceList, updateWrokPlaceLinkDeparts, getByEmployeeNum, getBranch, getOrgs, getWorkPlace, getOrgsList } from "../api/index";
import serviceAPI from "@/serviceAPI.js";
import { mapMutations } from 'vuex';
import { isMD5 } from "validator";
export default {
  data() {
    return {
      // branch: '',
      enterprises: [],
      byNum: 0,
      activeName: "岗位信息",
      dialogCheckResult: false,
      checkResult: [], //岗位检测结果
      excelMills: [], //excel导出的数据
      changStationReason: "", // 转岗原因
      timestamp: "", //转岗时间
      harmFactors: [], //危害因素
      employeeId: "", // 员工id
      expandedIds: [],
      newMills: [],
      correspondingMills: [],
      deleteEmployeeIds: [],
      selectStationIds: [], //所选岗位级联id数组
      moveEmployeesDialog: false, //人员转岗对话框
      moveEmployeeKey: Date.now(), //用于强制重新渲染move-employee组件
      unitCodeDialogVisible: false, // 编码对话框可见性
      unitCodeForm: { // 编码表单数据
        name: "",
        unitCode: "",
      },
      employeeMoveId: "",
      showOpen: {},
      initCount: 8,
      showEmployeeCount: {}, //显示得人员数量
      checkAll: false,
      hascheck: true,
      haveEmployee: true,
      nameLabel: "车间/厂房名称",
      ruleForm: {
        _id: "", //岗位id
        customizeHarm: "", //自定义危害因素
        harmFactors: "",
        workWay: "",
        peopleNumber: "",
        dailyProduce: "",
        contactTime: "",
        value: "小时",
        time: "",
        protectiveEquipment: "",
        protectiveFacilities: "",
        workTimeDay: 0, // h/d
        workTimeWeek: 0, // h/w
        workDayWeek: 0, // d/w
        customizeProduce: "", //自定义班制
        workType: "", //工种
      },
      rightMenu: {},
      repeatError: "",
      copyContent: {},
      showRightMenu: false, //是否显示右键菜单
      nameForm: {
        name: "",
        category: "",
      },
      rules: {
        name: [
          {
            required: true,
            message: "请输入名称",
            trigger: "blur",
          },
        ],
        category: [
          {
            required: true,
            message: "请选择类型",
          },
        ],
        time: [
          {
            message: "请输入数字类型",
            pattern: /^[0-9]+(\.?[0-9]+)?$/,
          },
        ],
      },
      form: {
        name: "",
      },
      employees: [],
      loading: false,
      value: "",
      activeNode: {},
      title: "添加厂房/车间",
      addPlant: "添加厂房/车间",
      mill: "厂房",
      workspaces: "车间",
      stations: "岗位",
      name: "", //输入框厂房名称
      dialogVisible: false, //添加厂房对话框
      dialogFormVisible: false, //添加表单输入框
      dialogFormVisible1: false, //添加表单输入框
      editText: "", //修改输入框的内容
      activeId: null, //点击修改当前的id
      nowInDepartId: "",// 当前所在depart
      showSearch: false,
      filterText: "",
      defaultProps: {
        children: null,
        label: "name",
        isLeaf(data, node) {
          if (data.hasChildren !== undefined) {
            return !data.hasChildren;
          }
          return false;
        },
      },
      allowDrop(draggingNode, dropNode, type) {
        if (draggingNode.level === dropNode.level) {
          if (draggingNode.parent.id === dropNode.parent.id) {
            // 向上拖拽 || 向下拖拽
            return type === "prev" || type === "next";
          }
        } else {
          // 不同级进行处理
          return false;
        }
      },
      allowDrag(node){
        return node.data.category !== 'enterprises';
      },
      materialList: [],
      originalEmployeeIds: [], // 保存打开对话框时的原始员工ID列表
      addedEmployeeIds: [], // 新增的员工ID
      treeKey: 0,
      isMultiEnterprise: false, // 是否为多企业环境
    };
  },
  components: {
    statisticInfo,
    moveEmployee,
    employeeInfo,
    stationForm,
    checkResult,
    materialInfo,
  },
  computed: {
    branch() {
      return localStorage.getItem('branch') || '';
    }
  },
  watch: {
    async moveEmployeesDialog(val) {
      if (!val) {
        this.selectStationIds = [];
      }
    },
    editText(val) {
      if (this.activeNode && this.activeNode.data) {
        this.activeNode.data.name = val;
      }
    },

    filterText(val) {
      this.$refs.tree.filter(val);
    },
    async activeNode(val) {
      console.log(val, "activeNode");
      
      if (val.data) {
        if (val.data.category === "mill") {
          console.log(1);
          this.addPlant=`添加${this.workspaces}`
          this.title = `添加${this.workspaces}`;
          this.label = `所属${this.mill}`;
          this.nameLabel = `${this.workspaces}名称`;
        } else if (val.data.category === "workspaces") {
          console.log(2);
           this.addPlant=`添加${this.stations}`
          this.title = `添加${this.stations}`;
          this.label = `所属${this.workspaces}`;
          this.nameLabel = `${this.stations}名称`;
        } else if( val.parent && val.parent.data && val.parent.data.category === 'mill'){
          console.log(3);
          this.title = `添加${this.stations}`;
          this.label = `所属${this.workspaces}`;
          this.nameLabel = `${this.stations}名称`;
        } else if (val.data.category === "stations" ) {
          if (val.data.children && val.data.children.length > 0) {
            this.employees = val.data.children
            }
          this.addPlant=`添加员工`
          this.label = `所属${this.stations}`;
          this.title = "添加员工";
          this.nameLabel = "员工姓名";
          this.nameForm.name = val.data.children ? val.data.children.map((item) => item._id) : [];
        }
      }
    },
    async dialogFormVisible(val) {
      if (val) {
        const fullId = this.activeNode.data.fullId;
        const res = await this.$axios.get(serviceAPI.findStationById, { fullId }, { hideLoading: true });
        if (res.data.code === 200) {
          this.ruleForm = res.data.data;
          this.ruleForm.peopleNumber = this.ruleForm.children.length
          this.ruleForm.value = this.ruleForm.value
            ? this.ruleForm.value : "小时";
          this.ruleForm.harmFactors =  this.ruleForm.harmFactors && this.ruleForm.harmFactors.length > 0 ? this.ruleForm.harmFactors.map((item) => item[1]) : []
        }
      }
    },
    async dialogVisible(val) {
      if (!val) {
        // dialog关闭时重置数据
        this.$refs.nameForm && this.$refs.nameForm.resetFields();
        this.originalEmployeeIds = [];
        this.addedEmployeeIds = [];
        this.deleteEmployeeIds = [];
        this.repeatError = "";
      }
      if (val && this.title === "添加员工") {
        // 适配新数据结构：员工直接作为子节点
        const employees = this.activeNode.data.children || [];
        // 保存当前选中员工列表作为原始状态
        this.originalEmployeeIds = employees.map(item => item._id);
        // 预设当前值
        this.nameForm.name = [...this.originalEmployeeIds];
      }
    },
  },
  async created() {
    await this.getDepartList()
  },
  async mounted() {
    // await this.findAllHarmFactors();
    this.$nextTick(() => {
      const treeDom = document.getElementsByClassName("millLeft")[0];
      const middleLineDom = document.getElementsByClassName("middleLine")[0];
      const employeeInfoDom = document.getElementsByClassName("rightInfo")[0];
      if (middleLineDom) {
        middleLineDom.onmousedown = (event) => {
        document.onmousemove = (ev) => {
          middleLineDom.style.cursor = "col-resize";
          var l = ev.clientX;
          treeDom.style.width = l - this.getElementLeft(treeDom) + "px";
          employeeInfoDom.style.width = document.body.offsetWidth - l + "px";
        };
        document.onmouseup = function () {
          document.onmousemove = null;
        };
        const treeDomWidth = treeDom.offsetWidth;
        };
      }
    });
    if (location.search.length > 0) {
      //判断地址参数是否传入
      this.getQueryStringArgs();
    }
    if (this.branch === 'by' || this.branch === 'wh') {
      const res = await getByEmployeeNum();
      if (res.status === 200 ) {
        this.byNum = res.data;
      }
    }
    const workplaceName = await getWorkPlace();
    if (workplaceName.status === 200) {
      console.log(workplaceName.data, '工作场所组成');
      this.addPlant = `添加${workplaceName.data.mill}/${workplaceName.data.workspaces}`;
      this.activeName = `${workplaceName.data.stations}信息`;
      this.mill = workplaceName.data.mill;
      this.workspaces = workplaceName.data.workspaces;
      this.stations = workplaceName.data.stations;
    }
    await this.getTemplate();
  },
  methods: {
    ...mapMutations(['updateDeparts']),

    // 判断节点是否为企业节点
    isEnterpriseNode(node) {
      return node && node.data && node.data.category === 'enterprises';
    },
    // 获取选择的岗位ID数组（替代isGroup判断）
    getFilteredStationIds(ids) {
      if (!ids || ids.length === 0) return [];

      // 如果是多企业环境且第一个ID是企业ID，则去掉第一个
      if (this.isMultiEnterprise && ids.length > 1) {
        const firstNode = this.$refs.tree.getNode(ids[0]);
        if (this.isEnterpriseNode(firstNode)) {
          return ids.slice(1);
        }
      }

      return ids;
    },
    async getTemplate() {
      const res = await this.$axios.get(serviceAPI.getTemplate);
      if (res.data.code === 200) {
        localStorage.millExcelTemp = res.data.data;
      }
    },
    async loadLazyData(node, resolve) {
      try {
        let params = {};
        console.log(node, 'node');
        if (node.level === 0) {
          params = {};
        } else {
          params = { parentId: node.data._id, showEmployee: true };
        }
        const res = await this.$axios.get(serviceAPI.findMillConstructionLazy, params, { hideLoading: true });
                
        if (res.data.status === 200) {
          const data = res.data.data || [];
          
          // 如果是加载岗位下的员工，设置默认显示数量
          if (node.data && node.data.category === 'stations') {
            // 设置默认只显示initCount个员工
            if (!this.showEmployeeCount[node.data._id]) {
              this.$set(this.showEmployeeCount, node.data._id, this.initCount);
            }
            this.$set(this.showOpen, node.data._id, true);
            
            // 延迟执行，确保DOM已更新
            setTimeout(() => {
              this.getDisplayNode();
            }, 0);
          }
          if(node.level===0 && data.length>0){
            this.isMultiEnterprise = data[0].category === 'enterprises';
          }
          
          resolve(data);
        } else {
          console.error('加载失败:', res.data.message);
          resolve([]);
        }
      } catch (error) {
        console.error('数据加载失败:', error);
        resolve([]);
      }
      this.clickleft(this.activeNode.data,this.activeNode)
    },
    // 处理数据--wx
    processData(data) {
      if (!Array.isArray(data)) return [];
      
      return data.map(item => {
        const newItem = { ...item };
        
        if (newItem.children && Array.isArray(newItem.children)) {
          // 递归处理子节点
          newItem.children = this.processData(newItem.children);
          
          // 如果当前节点的 category 是 stations，则移除 children 属性
          if (newItem.category === 'stations') {
            delete newItem.children;
          }
        }
        
        return newItem;
      });
    },
    //体检禁忌证传参搜索处理--wx
    getQueryStringArgs() {
      let qs = location.search.length > 0 ? location.search.substring(1) : "",
        item = qs.split("=");
      let name = null;
      let value = null;
      name = decodeURIComponent(item[0]);
      value = decodeURIComponent(item[1]); //执行解码，因为中文字符串往往在传递时被编码过了
      this.filterText = value;
    },
    recursionFn(millConstructions, names = {}) {
      if (millConstructions && millConstructions.length > 0) {
        return millConstructions.map(item => {
          let newItem = { ...item }; // 创建一个新的对象
          let newNames = {...names, [item.category]:item.name}; // 添加当前元素的 name 到 names 数组
          if (newItem.category === 'stations') {
            const length = newItem.children.length;
            const employeesUnitCode = newItem.children.map(item => item.employees.unitCode||item.employees._id).join(';');
            // delete newItem.children; // 删除新对象的 children 属性
            this.excelMills.push({...newItem, names: newNames, person:length, employeesUnitCode}); // 添加到 excelMills 数组
          } else if (newItem.children) {
            newItem.children = this.recursionFn(newItem.children, newNames); // 递归处理新对象的 children 属性
          }
          return newItem;
        });
      } else {
        return [];
      }
    },
    async jsonToExcel() {
      let res = await this.$axios.post(serviceAPI.findMillConstruction);
      if (res.data.code === 200) {
        this.recursionFn(res.data.data);
      }
      
      /* 需要导出的JSON数据 *
      /* 如果没有导入xlsx组件则导入 */
      if (typeof XLSX == "undefined") XLSX = require("xlsx");
      const excelTable  = [];
      this.excelMills.forEach((item) => {
        
        excelTable.push({
          ...this.isMultiEnterprise? {'企业' : item.names.enterprises}: {},
          [this.mill]: item.names.mill || "",
          [this.workspaces]: item.names.workspaces || "",
          [this.stations]: item.name || "",
          工种: item.workType || "",
          人员编码: item.employeesUnitCode || "",
          人员姓名: item.children.map(item=>item.employees.name).join(";") || "",
          接触的职业病危害因素名称: item.customizeHarm
            ? item.harmFactors.map((curItem) => curItem[1]).join("、") +
              "、" +
              item.customizeHarm
            : item.harmFactors.map((curItem) => curItem[1]).join("、"),
          ["总接触人数（人）"]: item.person,
          班制: item.customizeProduce || item.dailyProduce ||"",
          接触时间: `${item.workTimeDay || 0}h/d ${item.workDayWeek || 0}d/w`,
          作业方式: item.workWay || "",
          职业病防护设施及运行情况: item.protectiveFacilities || "",
          个人防护用户发放及使用情况: item.protectiveEquipment || "",
        });
      });
      /* 创建worksheet */
      var ws = XLSX.utils.json_to_sheet(excelTable);
      /* 新建空workbook，然后加入worksheet */
      var wb = XLSX.utils.book_new();
      XLSX.utils.book_append_sheet(wb, ws, "People");
      /* 生成xlsx文件 */
      XLSX.writeFile(wb, "工作场所.xlsx");
    },
    async findAllHarmFactors() {
      let res = await this.$axios.get(serviceAPI.findAllHarmFactors);
      if (res.data.code === 200) {
        this.harmFactors = res.data.data;
        this.$store.commit("getHarmFactors", res.data.data);
      }
    },
    // 获取元素到页面最左侧的距离
    getElementLeft(element) {
      var actualLeft = element.offsetLeft;
      var current = element.offsetParent;
      while (current !== null) {
        actualLeft += current.offsetLeft;
        current = current.offsetParent;
      }
      return actualLeft;
    },
    onRemoveTag(removedId) {
      // 如果是原始列表中已有的员工，才标记为需要删除
      if (this.originalEmployeeIds.includes(removedId)) {
        this.deleteEmployeeIds.push(removedId);
      }
      
      // 如果是刚添加的员工被移除，从添加列表移除
      const addIndex = this.addedEmployeeIds.indexOf(removedId);
      if (addIndex !== -1) {
        this.addedEmployeeIds.splice(addIndex, 1);
      }
    },
    //取消移动岗位
    cancalMove() {
      this.hascheck = true;
      this.$refs.tree.setCheckedNodes([]);
      this.employeeMoveId = "";
      this.$nextTick(() => {
        let dislayCheckNode = document.getElementsByClassName("displayCheck");
        for (let i = 0; i < dislayCheckNode.length; i++) {
          dislayCheckNode[i].previousElementSibling.style.display = "none";
        }
      });
    },
    //获取所选岗位的树形数组
    getSelectIds(ids) {
      this.selectStationIds = this.getFilteredStationIds(ids);
      console.log(this.selectStationIds, 'selectStationIds');

    },
    getReason(reason) {
      this.changStationReason = reason;
    },
    getTime(time) {
      this.timestamp = time;
    },
    //传files附件值
    getFiles(val) {
      if (val[0].response) {
        const files = [];
        for (let i = 0; i < val.length; i++) {
          files.push({
            originName: val[i].response.files[0].originName,
            staticName: val[i].response.files[0].staticName,
          });
        }
        this.files = files;
      }
    },
    // 保存转岗
    async saveMoveEmployee() {
      this.moveEmployeesDialog = false;  
      let enterpriseId = this.activeNode.data && this.activeNode.data.EnterpriseID;
      let millId = this.activeNode.parent && this.activeNode.parent.parent && this.activeNode.parent.parent.data && this.activeNode.parent.parent.data.category==='mill'?this.activeNode.parent.parent.data._id:undefined;
      let workspaceId = this.activeNode.parent && this.activeNode.parent.data ? this.activeNode.parent.data._id : undefined;
      let stationId = this.activeNode.data._id;
      let employeeId = this.$refs.tree
        .getCheckedNodes()
        .map((item) => item._id);
      let beforePosition = this.activeNode.data.name;
      // let nowPosition = "";
      let res = await this.$axios.post(serviceAPI.moveEmployees, {
        enterpriseId,
        millId,
        workspaceId,
        stationId,
        employeeId,
        beforePosition,
        selectStationIds: this.selectStationIds,
        timestamp: this.timestamp || new Date(),
        changStationReason: this.changStationReason, //转岗原因
        files: this.files,
      });
      if (res.data.code === 200) {
        this.$message.success(res.data.message);
        this.findMillConstruction();
        this.employeeMoveId = "";
        this.$nextTick(() => {
          let dislayCheckNode = document.getElementsByClassName("displayCheck");
          for (let i = 0; i < dislayCheckNode.length; i++) {
            dislayCheckNode[i].previousElementSibling.style.display = "none";
          }
        });
      } else if (res.data.code === 201) {
        this.$message.warning(res.data.message);
      } else {
        this.$message.error(res.data.message);
      }
    },
    getRemoveEmployees(node) {
      this.openMoveDialog();
    },
    // 打开转岗对话框
    openMoveDialog() {
      this.moveEmployeeKey = Date.now(); // 更新key强制重新渲染组件
      this.moveEmployeesDialog = true;
    },
    // 人员批量转岗
    employeeMove(node) {
      this.hascheck = true;
      this.$refs.tree.setCheckedNodes([]);
      this.activeNode = node;
      this.employeeMoveId = node.data._id;
      this.$nextTick(() => {
        let checkNode = document.getElementsByClassName("showCheck");
        for (let i = 0; i < checkNode.length; i++) {
          checkNode[i].previousElementSibling.style.display = "block";
        }
        let dislayCheckNode = document.getElementsByClassName("displayCheck");
        for (let i = 0; i < dislayCheckNode.length; i++) {
          dislayCheckNode[i].previousElementSibling.style.display = "none";
        }
      });
    },
    showNode(node) {
      this.$set(this.showOpen, node.parent.data._id, false);
      this.showEmployeeCount[node.parent.data._id] = node.parent.data.children.length;
      this.$nextTick(() => {
        this.getDisplayNode();
      });
    },
    hideNode(node) {
      this.$set(this.showOpen, node.parent.data._id, true);
      this.showEmployeeCount[node.parent.data._id] = this.initCount;
      this.$nextTick(() => {
        this.getDisplayNode();
      });
    },
    handleCheckAllChange(val) {
      if (val) {
        // 直接从树组件获取顶级节点
        const rootNode = this.$refs.tree.store.root;
        if (rootNode && rootNode.childNodes) {
          const topNodeIds = rootNode.childNodes.map(node => node.data._id);
          this.$refs.tree.setCheckedKeys(topNodeIds);
          this.hascheck = false;
        }
      } else {
        // 取消全选
        this.$refs.tree.setCheckedKeys([]);
        this.hascheck = true;
      }
    },
    oncheck() {
      // this.activeNode = {}
      this.hascheck = this.$refs.tree.getCheckedKeys().length === 0 ? true : false;
    },
    deleteNode() {     
      const text = this.isMultiEnterprise ? "确定删除该公司所有下属厂房/车间?" : "确定删除选中项?";
      this.$confirm(text, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
      .then(async () => {
        // 获取选中节点的ID
        const checkedKeys = this.$refs.tree.getCheckedKeys();
        const params = this.isMultiEnterprise ? {
          nodeIds: checkedKeys,
        } : {
          enterpriseId: this.$refs.tree.getNode(checkedKeys[0]) && this.$refs.tree.getNode(checkedKeys[0]).data ? this.$refs.tree.getNode(checkedKeys[0]).data.EnterpriseID : '',
          millId: checkedKeys,
        }
        const res = await this.$axios.post(serviceAPI.deleteMill, params);
        if (res.data.code === 200) {
            this.$message.success("删除成功");
            this.findMillConstruction();
            this.hascheck = true;
            this.checkAll = false;
          } else {
            this.$message.error("删除失败");
          }
        })
        .catch((err) => {
          console.log(err);
          this.$message({
            type: "info",
            message: "已取消删除",
          });
        });
    },
    gotoExcelImport() {
      this.$router.push({
        name: "importConstruction",
        params: {
          workplaceConfig: {
            mill: this.mill,
            workspaces: this.workspaces,
            stations: this.stations
          }
        }
      });
    },
    gotoHarmFactor() {
      this.$router.push({
        name: "importHarmFactors",
      });
    },
    async upload() {
      if (!this.ruleForm.time) this.ruleForm.value = "";
      if (this.ruleForm.workTimeDay && this.ruleForm.workDayWeek) {
        this.ruleForm.workTimeWeek =
          Number(this.ruleForm.workTimeDay) * Number(this.ruleForm.workDayWeek);
      }
      let stationId = this.activeNode.data._id;
      let workspaceId = this.activeNode.parent && this.activeNode.parent.data ? this.activeNode.parent.data._id : undefined;
      let millId = this.activeNode.parent && this.activeNode.parent.parent && this.activeNode.parent.parent.data && this.activeNode.parent.parent.data.category==='mill'?this.activeNode.parent.parent.data._id:undefined;
      
      // 获取当前节点的EnterpriseID
      const enterpriseId = this.activeNode.data.EnterpriseID;
      
      const res = await this.$axios.post(serviceAPI.editStation, {
        enterpriseId,
        millId,
        workspaceId,
        stationId,
        ...this.ruleForm,
      });
      if (res.data.status === 200) {
        this.$message.success("修改成功");
        // 只刷新当前节点的父节点，避免全树刷新
        if (this.activeNode && this.activeNode.parent) {
          this.refreshNode(this.activeNode.parent);
        }
      } else {
        this.$message.error("服务器出错");
      }
      this.dialogFormVisible = false;
    },
   async remoteMethod(query) {
        if (query !== '') {
          this.loading = true;
          const enterpriseId = this.activeNode && this.activeNode.data ? this.activeNode.data.EnterpriseID : '';
         await this.findAllEmployees({ name: query, enterpriseId });
        } else {
          this.employees = [];
      }
    },
    onNodeExpand(data, node) {
      this.expandedIds.push(data._id);
      if (data.category === "stations") {
        // 设置默认显示的员工数量
        if (!this.showEmployeeCount[data._id]) {
          this.$set(this.showEmployeeCount, data._id, this.initCount);
        }
        // 默认设置为true，表示需要显示"展开"按钮
        this.$set(this.showOpen, data._id, true);
        
        // 确保在DOM更新后应用显示/隐藏逻辑
        this.$nextTick(() => {
          this.getDisplayNode();
        });
      }
    },
    onNodeCollapse(data) {
      // 当节点关闭时
      this.expandedIds.pop(data._id);
    },
    //点击鼠标左键隐藏
    async clickleft(data, node) {
      console.log(data, node, "clickleft");
      this.activeNode = node;
      this.$nextTick(() => {
        document.getElementsByClassName("rightInfo")[0].scrollTop = 0;
      });
      if (data.category === "stations") {
        const fullId = this.activeNode.data.fullId;
        const result = await this.$axios.get(serviceAPI.findStationById, { fullId }, { hideLoading: true });
        let harmFactors = [];
        if (result.data.code === 200) {
          this.ruleForm = result.data.data;
          harmFactors = JSON.stringify(result.data.data.harmFactors)
          this.ruleForm.peopleNumber = this.ruleForm.children.length
          this.ruleForm.value = this.ruleForm.value
            ? this.ruleForm.value : "小时";
          this.ruleForm.harmFactors =  this.ruleForm.harmFactors.map((item) => item[1])
        }
        console.log('点击这里')
        // 获取职业病危害因素危害检测
        const res = await this.$axios.post(serviceAPI.getCheckResult, [
          {
            name: data.name,
            harmFactorsAndSort: JSON.parse(harmFactors),
            workspace: node.parent && node.parent.data ? node.parent.data.name : '',
          },
        ], { hideLoading: true });
        if (res.data.code === 200) {
          this.$store.commit("getCheckResult", res.data.data.res);
          this.materialList = res.data.data.materialInfoList;
        }
      } else if (node.parent && node.parent.data && node.parent.data.category === "stations") {
        // 适配新数据结构：员工数据直接在data中
        // this.$refs.employeeInfo.findEmployeeById();
        // this.$refs.employeeInfo.findEmployeeWorkChanges();
        // this.$refs.employeeInfo.findCheckResult();
        this.employeeId = data._id;
      }
      if (this.showRightMenu) {
        this.showRightMenu = false;
      }
    },
    clickright(MouseEvent, object, Node, VueComponent) {
      this.rightMenu = {
        top: MouseEvent.clientY + 10 + "px",
        left: MouseEvent.clientX + 10 + "px",
      };
      this.activeNode = Node;
      this.showRightMenu = !this.showRightMenu;
    },
    handleDelete(node, data) {
      this.activeNode = node;
      let category = "";
      if (this.activeNode.data.category === "mill") {
        // category = "厂房";
        category = this.mill;
      } else if (this.activeNode.data.category === "workspaces") {
        // category = "车间";
        category = this.workspaces;
      } else if (this.activeNode.data.category === "stations") {
        // category = "岗位";
        category = this.stations;
      } else if (this.activeNode.data.category === 'employees') {
        category = "人员";
      }
      this.$confirm("您将删除" + category + ":" + this.activeNode.label + "?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(async () => {
          let res = {};
          const enterpriseId = this.activeNode.data && this.activeNode.data.EnterpriseID;
          if (this.activeNode.data.category === "stations") {
            let stationId = this.activeNode.data._id;
            let workspaceId = this.activeNode.parent && this.activeNode.parent.data ? this.activeNode.parent.data._id : undefined;
            let millId = this.activeNode.parent && this.activeNode.parent.parent && this.activeNode.parent.parent.data && this.activeNode.parent.parent.data.category==='mill'?this.activeNode.parent.parent.data._id:undefined;
            res = await this.$axios.post(serviceAPI.deleteStation, {
              enterpriseId,
              workspaceId,
              millId,
              stationId,
            });
          } else if (this.activeNode.data.category === "workspaces") {
            let workspaceId = this.activeNode.data._id;
            let millId = this.activeNode.parent && this.activeNode.parent.data && this.activeNode.parent.data.category==='mill'?this.activeNode.parent.data._id:undefined;
            res = await this.$axios.post(serviceAPI.deleteWorkspace, {
              enterpriseId,
              workspaceId,
              millId,
            });
          } else if (this.activeNode.data.category === "mill") {
            res = await this.$axios.post(serviceAPI.deleteMill, {
              enterpriseId,
              millId: [this.activeNode.data._id],
            });
          } else if (this.activeNode.data.category === 'employees') {
            let employeeId = this.activeNode.data._id;
            let stationId = this.activeNode.parent && this.activeNode.parent.data ? this.activeNode.parent.data._id : undefined;
            let workspaceId = this.activeNode.parent && this.activeNode.parent.parent && this.activeNode.parent.parent.data ? this.activeNode.parent.parent.data._id : undefined;
            let millId = this.activeNode.parent && this.activeNode.parent.parent && this.activeNode.parent.parent.parent && this.activeNode.parent.parent.parent.data && this.activeNode.parent.parent.parent.data.category==='mill'?this.activeNode.parent.parent.parent.data._id:undefined;
            res = await this.$axios.post(serviceAPI.deleteEmployee, {
              employeeId,
              stationId,
              workspaceId,
              millId,
            });
          }
          if (res.data.code === 200) {
            this.findMillConstruction();
            this.$message.success("删除成功");
          } else {
            this.$message.error(res.data.message);
          }
        })
        .catch((err) => {
          console.log(err);
          this.$message({
            type: "info",
            message: "已取消删除",
          });
        });
    },
    //查询员工
    async findEmployees(stationsName, workspaceName, millName, enterpriseId) {
      let res = await this.$axios.post(serviceAPI.findEmployees, {
        station: stationsName,
        workspaceName,
        millName,
        enterpriseId,
      });
      if (res.data.code === 200) {
        
        this.nameForm.name = res.data.data.map((item) => {
          return item._id;
        });
      }
      this.$nextTick(() => {
        console.log(this.activeNode.data.children);
        // 适配新数据结构：员工直接作为子节点
        const employees = this.activeNode.data.children
        employees.forEach((item) => {
          this.nameForm.name.push(item._id);
        });
        console.log(this.nameForm.name);
        this.nameForm.name = [...new Set(this.nameForm.name)];

      });
    },
    async findAllEmployees(params) {
      console.log(params)
      let res = await this.$axios.post(serviceAPI.findEmployees, {
          name:params.name
        }, { hideLoading: true });
      if (res.data.code === 200) {
        this.employees = res.data.data;
          this.loading = false;
        }
    },
    async findMillConstruction() {
      this.treeKey = Date.now()
      this.$nextTick(() => {
        this.refreshTree()
      })
    },
    getDisplayNode() {
      let contentnodes = document.getElementsByClassName("el-tree-node__content");
      for (let i = 0; i < contentnodes.length; i++) {
        contentnodes[i].style.display = "flex";
      }
      let displayNodes = document.getElementsByClassName("displayNode");
      for (let i = 0; i < displayNodes.length; i++) {
        if (displayNodes[i].parentNode) {
          displayNodes[i].parentNode.style.display = "none";
        }
      }
    },
    //添加
    async addMill() {
      let formvalid = true;
      this.$refs.nameForm.validate((valid) => {
        formvalid = valid;
      });
      if (!formvalid) return this.$message.error("请信息填写完整后提交");
      if (this.repeatError) return;
      this.dialogVisible = false;
      let res = {};
      if (this.title === "添加厂房/车间" || this.title === "添加厂房/装置") {
        res = await this.$axios.post(serviceAPI.addMill, {
          enterpriseId: this.nameForm.EnterpriseID,
          mills: [
            {
              name: this.nameForm.name,
              category: this.nameForm.category,
            },
          ],
        });
      } else if (this.title === "添加车间" || this.title === "添加装置") {
        res = await this.$axios.post(serviceAPI.addWorkSpace, {
          workspace: {
            name: this.nameForm.name,
          },

          millId: this.activeNode.data._id,
        });
      } else if (this.title === "添加岗位" || this.title === "添加工序") {
        // 获取当前节点的EnterpriseID
        const enterpriseId = this.activeNode.data && this.activeNode.data.EnterpriseID;
        res = await this.$axios.post(serviceAPI.addStation, {
          enterpriseId,
          millName: this.activeNode.parent && this.activeNode.parent.data && this.activeNode.parent.data.category==='mill'?this.activeNode.parent.data.name:undefined,
          workspaceName: this.activeNode.data.name,
          station: {
            name: this.nameForm.name,
          },
          millId: this.activeNode.parent && this.activeNode.parent.data && this.activeNode.parent.data.category==='mill'?this.activeNode.parent.data._id:undefined,
          workspaceId: this.activeNode.data._id,
        });
      } else if (this.title === "添加员工") {
        let stationId = this.activeNode.data._id;
        let stationsName = this.activeNode.data.name;
        let workspaceId = this.activeNode.parent && this.activeNode.parent.data ? this.activeNode.parent.data._id : undefined;
        let millId = this.activeNode.parent && this.activeNode.parent.parent && this.activeNode.parent.parent.data && this.activeNode.parent.parent.data.category === 'mill' ? this.activeNode.parent.parent.data._id : undefined;
        
        // 获取当前节点的EnterpriseID
        const enterpriseId = this.activeNode.data && this.activeNode.data.EnterpriseID;

        // 检查是否有变更
        if (this.addedEmployeeIds.length === 0 && this.deleteEmployeeIds.length === 0) {
          this.$message.info("没有员工变更，无需提交");
          return;
        }

        res = await this.$axios.post(serviceAPI.addEmployee, {
          enterpriseId,
          stationId,
          workspaceId,
          millId,
          // 只发送变更的部分
          addedEmployeeIds: this.addedEmployeeIds,
          deleteEmployeeIds: this.deleteEmployeeIds,
          stationsName,
        });
      }
      if (res.data.code === 200) {
        this.findMillConstruction();
        this.$message.success("添加成功");
        this.nameForm.name = "";
      } else {
        this.$message.error(res.data.message);
      }
      this.name = "";
    },
    onchange(node) {
      this.activeId = null;
      this.handleEdit({}, node);
    },
    edit(node) {
      this.activeId = node.id;
      this.activeNode = node;
      this.editText = node.label;
      setTimeout(() => {
        this.$refs.editInput && this.$refs.editInput.select();
      }, 100);
    },
    async handleEdit(params = {}, node) {
      this.activeNode = node;
      let res = {};
      // 获取当前节点的EnterpriseID
      const enterpriseId = this.activeNode.data && this.activeNode.data.EnterpriseID;
      
      // 根据节点层级判断修改类型
      const nodeLevel = this.activeNode.level;
      
      //修改厂房
      if (nodeLevel === 1 || (this.activeNode.parent && this.activeNode.parent.data && this.activeNode.parent.data.category === 'enterprises' && nodeLevel === 2)) {
        res = await this.$axios.post(serviceAPI.editMill, {
          enterpriseId,
          millId: this.activeNode.data._id,
          status: params.status,
          name: !params.status ? this.editText : node.data.name,
          unitCode: node.data.unitCode, // 保留编码
        });
        //修改车间
      } else if (nodeLevel === 2 || (this.activeNode.parent && this.activeNode.parent.parent && this.activeNode.parent.parent.data && this.activeNode.parent.parent.data.category === 'enterprises' && nodeLevel === 3)) {
        res = await this.$axios.post(serviceAPI.editWorkspace, {
          millId: this.activeNode.parent && this.activeNode.parent.data ? this.activeNode.parent.data._id : undefined,
          status: params.status,
          workspaceId: this.activeNode.data._id,
          name: !params.status ? this.editText : node.data.name,
          unitCode: node.data.unitCode, // 保留编码
        });
        // 修改岗位
      } else if (nodeLevel === 3 || (this.activeNode.parent && this.activeNode.parent.parent && this.activeNode.parent.parent.parent && this.activeNode.parent.parent.parent.data && this.activeNode.parent.parent.parent.data.category === 'enterprises' && nodeLevel === 4)) {
        let stationId = this.activeNode.data._id;
        let workspaceId = this.activeNode.parent && this.activeNode.parent.data ? this.activeNode.parent.data._id : undefined;
        let millId = this.activeNode.parent && this.activeNode.parent.parent && this.activeNode.parent.parent.data ? this.activeNode.parent.parent.data._id : undefined;
        res = await this.$axios.post(serviceAPI.editStation, {
          enterpriseId,
          millId,
          workspaceId,
          status: params.status,
          stationId,
          name: !params.status ? this.editText : node.data.name,
          unitCode: node.data.unitCode, // 保留编码
        });
      }
      if (res.data.status === 200) {
        this.$message.success("修改成功");
        if (params.status) this.findMillConstruction();
      } else {
        this.$message.error(res.data.message);
      }
    },
    filterNode(value, data) {
      if (!value) return true;
      if (!data.name) {
        return data.employees && data.employees.name.indexOf(value) !== -1;
      } else {
        return data.name.indexOf(value) !== -1;
      }
    },
    showSearchInput() {
      this.showSearch = true;
    },
    async getDepartList() {
      try {
        const res = await getOrgs()

        if (res && res.data && res.data.orgsTree.length > 0) {
          await this.updateDeparts({
            orgsTree: res.data.orgsTree,
          })
        }
      } catch (error) {
        console.log(333, error)
      }
    },
    // 同步绑定的部门人员到当前车间岗位
    async updateDepartsLink() {
      const res = await updateWrokPlaceLinkDeparts()
      if (res && res.data) {
        this.$message(res.data)
        await this.findMillConstruction()
      }
    },
    // 前往by人员审核列表
    gotoByReview(){
      this.$router.push({ name: "byReviewList" });
    },
    // 打开编码对话框
    editMillUnitCode(node) {
      this.unitCodeForm = {
        name: node.label,
        unitCode: node.data.unitCode || "",
        encode: node.data.encode || "",
      };
      this.unitCodeDialogVisible = true;
      this.activeNode = node;
    },
    
    // 保存编码
    async saveUnitCode() {
      let res = {};
      const { unitCode, encode } = this.unitCodeForm;
      
      // 获取当前节点的EnterpriseID
      const enterpriseId = this.activeNode.data && this.activeNode.data.EnterpriseID;
      
      // 根据节点层级判断修改类型
      const nodeLevel = this.activeNode.level;
      
      // 修改厂房
      if (nodeLevel === 1 || (this.activeNode.parent && this.activeNode.parent.data && this.activeNode.parent.data.category === 'enterprises' && nodeLevel === 2)) {
        res = await this.$axios.post(serviceAPI.editMill, {
          enterpriseId,
          millId: this.activeNode.data._id,
          unitCode, 
          encode,
        });
        //修改车间
      } else if (nodeLevel === 2 || (this.activeNode.parent && this.activeNode.parent.parent && this.activeNode.parent.parent.data && this.activeNode.parent.parent.data.category === 'enterprises' && nodeLevel === 3)) {
        res = await this.$axios.post(serviceAPI.editWorkspace, {
          millId: this.activeNode.parent && this.activeNode.parent.data ? this.activeNode.parent.data._id : undefined,
          workspaceId: this.activeNode.data._id,
          unitCode, 
          encode,
        });
        // 修改岗位
      } else if (nodeLevel === 3 || (this.activeNode.parent && this.activeNode.parent.parent && this.activeNode.parent.parent.parent && this.activeNode.parent.parent.parent.data && this.activeNode.parent.parent.parent.data.category === 'enterprises' && nodeLevel === 4)) {
        let stationId = this.activeNode.data._id;
        let workspaceId = this.activeNode.parent && this.activeNode.parent.data ? this.activeNode.parent.data._id : undefined;
        let millId = this.activeNode.parent && this.activeNode.parent.parent && this.activeNode.parent.parent.data ? this.activeNode.parent.parent.data._id : undefined;
        res = await this.$axios.post(serviceAPI.editStation, {
          enterpriseId,
          millId,
          workspaceId,
          stationId,
          unitCode, 
          encode,
        });
      }
      
      if (res.data && res.data.status === 200) {
        this.$message.success("编码保存成功");
        this.unitCodeDialogVisible = false;
        this.findMillConstruction(); // 刷新数据
      } else {
        this.$message.error(res.data ? res.data.message : "保存失败，请重试");
      }
    },
    openAddMill() {
      this.activeNode = {};
      this.title = `添加${this.mill}/${this.workspaces}`;
      this.addPlant = this.title
      this.nameLabel = '名称';
      // 重置表单数据
      this.nameForm = {
        name: "",
        category: "",
        EnterpriseID: ""
      };
      if(this.isMultiEnterprise) {
        this.getOrgsList();
      }
      this.dialogVisible = true;
    },

    // 统一的dialog打开方法
    openDialog(node) {
      // 先设置activeNode，触发watcher更新相关状态
      this.activeNode = node;

      // 等待下一个tick确保状态更新完成
      this.$nextTick(() => {
        // 重置表单数据
        this.nameForm = {
          name: "",
          category: "",
          EnterpriseID: ""
        };

        // 如果是添加员工，需要特殊处理
        if (this.title === "添加员工" && node.data.children) {
          this.nameForm.name = node.data.children.map((item) => item._id);
        }

        // 最后打开dialog
        this.dialogVisible = true;
      });
    },
    async getOrgsList() {
      const res = await getOrgsList();
      this.enterprises = res.data.orgsTree;
      console.log(this.enterprises, 'enterprises');
    },
    onSelectChange(currentValue) {
      // 计算新增的员工
      this.addedEmployeeIds = currentValue.filter(id => !this.originalEmployeeIds.includes(id));
      
      // 计算取消删除的员工 (重新选择了之前标记为删除的)
      const readdedIds = currentValue.filter(id => this.deleteEmployeeIds.includes(id));
      
      // 从删除列表中移除重新选择的员工
      readdedIds.forEach(id => {
        const index = this.deleteEmployeeIds.indexOf(id);
        if (index !== -1) {
          this.deleteEmployeeIds.splice(index, 1);
        }
      });
    },
    refreshNode(node) {
      if (node && node.childNodes) {
        // 清空子节点
        node.childNodes = [];
        // 重新加载子节点
        node.loading = false;
        node.loaded = false;
      }
    },
    refreshTree() {
      // 获取根节点并刷新
      const rootNode = this.$refs.tree.store.root;
      if (rootNode) {
        this.refreshNode(rootNode);
      }
    },
  },
};
</script>

<style scoped>
.el-card {
  margin-bottom: 8px;
}
.clearfix:before,
.clearfix:after {
  display: table;
  content: "";
}
.clearfix:after {
  clear: both;
}
.clearfix span {
  font-size: 14px;
}

/* .box-card {
    width: 47%;
  } */
.json2excel {
  display: inline-block;
  margin-left: 10px;
}
.middleLine {
  width: 3px;
  background: #eee;
}
.middleLine:hover {
  cursor: col-resize;
}
.displayNode {
  display: none;
}
.millConstruction {
  padding: 29px;
  width: 100%;
  display: flex;
}
.millLeft {
  width: 39%;
  height: 82vh;
  overflow: auto;
}
.rightInfo {
  padding: 8px;
  width: 61%;
  height: 82vh;
  overflow: auto;
}

.el-icon-circle-plus-outline {
  color: #5cb6ff;
  font-size: 40px;
}

.addMill {
  padding-right: 10px;
  margin-bottom: 23px;
  display: flex;
  flex-wrap: wrap;
  margin-top: 10px;
}

.addMill .el-input {
  margin-top: 10px;
}

.custom-tree-node {
  display: flex;
  align-items: center;
  font-size: 12.5px;
  padding-right: 8px;
}

.rigthOperation {
  margin-left: 25px;
}

.el-link.el-link--default {
  color: #409eff;
  margin-right: 18px;
}

.el-tree .el-input {
  margin-left: 0;
}

.positionCard {
  position: fixed;
  display: block;
  z-index: 99;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  background: white;
}

.positionCard div {
  width: 6rem;
  padding: 0.925rem;
  display: flex;
  justify-content: space-between;
  color: #666;
  font-size: 13px;
}

.positionCard div:hover {
  background-color: #f7f7f7;
  color: black;
}

.addEmployee {
  color: #999;
}
.el-form-item {
  margin-bottom: 20px;
}
.byReview {
  margin-left: 10px;
}

/* .millConstruction .el-tree-node__children .el-checkbox {
  display: none;
} */
</style>
