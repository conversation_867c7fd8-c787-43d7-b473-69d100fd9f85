<!-- 防护用品领用记录表格组件 -->
<template>
  <div class="defend-table">
    <el-table
      :data="tableData"
      ref="table"
      border
      style="width: 100%"
      @selection-change="selectChange"
      stripe
    >
      <el-table-column
        :selectable="getSelectIndex"
        type="selection"
      ></el-table-column>
      <el-table-column
        label="序号"
        type="index"
        width="60"
        align="center"
      ></el-table-column>

      <el-table-column
        v-for="item in tableHeader"
        :key="item.label"
        :label="item.label"
        :prop="item.prop"
        :width="item.width"
        align="center"
      >
        <template slot-scope="scope">
          <span v-if="item.prop === 'date'">
            {{ formatDate(scope.row[item.prop]) }}
          </span>
          <span v-else-if="item.prop === 'studio'">
            {{ scope.row.workshopName ? scope.row.workshopName : '' }} {{ scope.row.workspacesName ? scope.row.workspacesName : '' }} {{ scope.row.workstationName ? scope.row.workstationName : '' }}
            {{ scope.row.departName ? scope.row.departName : ''  }}
          </span>
          <img v-else-if="item.prop === 'sign'" :src="scope.row.sign" :style="{ maxWidth: '160px', maxHeight: '50px' }">
          <div v-else-if="item.prop === 'products'">
            <div v-for="item in scope.row.products" :key="item._id" @click="handleProductClick(item)" class="tableContainer">
              {{ getProductDisplayName(item) }} × {{ item.number }}
            </div>
          </div>
          <span v-else>{{ scope.row[item.prop] }}</span>
        </template>
      </el-table-column>

      <el-table-column v-if="branch!=='wh'" label="操作" align="center" width="120" >
        <template slot-scope="scope">
          <el-button @click.prevent="handleDelete(scope.row)" type="danger" size="small"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页区域 -->
    <div class="pagebreak" v-show="showPagination">
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="currentPage"
        :page-sizes="[10, 20, 30]"
        :page-size="pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
      >
      </el-pagination>
    </div>
  </div>
</template>

<script>
import moment from "moment";
import { mapGetters } from "vuex";

export default {
  name: 'DefendTable',
  props: {
    // 表格数据
    tableData: {
      type: Array,
      default: () => []
    },
    // 总数据量
    total: {
      type: Number,
      default: 0
    },
    // 是否显示分页
    showPagination: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      currentPage: 1,
      pageSize: 10,
      multipleSelection: [],
      buttonShow: false,
      tableHeader: [
        {
          prop: "studio",
          label: "工作场所",
        },
        {
          prop: "products",
          label: "用品名称",
          width: "300",
        },
        {
          prop: "receiveStartDate",
          label: "开放领用日期",
          width: "180",
        },
        {
          prop: "employee",
          label: "领取人",
          width: "120",
        },
        {
          prop: "status",
          label: "状态",
          width: "120",
        },
        {
          prop: "receiveDate",
          label: "领取时间",
          width: "180",
        },
        {
          prop: "sign",
          width: "180",
          label: "签名",
        },
      ],
    };
  },
  computed: {
    ...mapGetters(["branch"]),
  },
  methods: {
    formatDate(e) {
      if (e && moment(e).isValid()) {
        return moment(e).format("YYYY-MM-DD");
      }
    },
    selectChange(selection) {
      this.buttonShow = selection.length > 0;
      this.multipleSelection = selection;
      this.$emit('selection-change', selection);
    },
    getSelectIndex(row, index) {
      row.index = index;
      return true;
    },
    handleDelete(row) {
      this.$emit('delete', row);
    },
    handleSizeChange(val) {
      this.pageSize = val;
      this.$emit('size-change', val);
    },
    handleCurrentChange(val) {
      this.currentPage = val;
      this.$emit('current-change', val);
    },
    handleProductClick(item) {
      this.$emit('product-click', item);
    },
    // 获取产品显示名称
    getProductDisplayName(item) {
      // 如果有具体产品名称，显示产品名称和型号
      if (item.product && item.product.trim()) {
        return `${item.product}${item.modelNumber ? '(' + item.modelNumber + ')' : ''}`;
      }

      // 如果没有具体产品名称，显示分类名称（配发标准阶段）
      if (item.categoryName) {
        return `[${item.categoryName}]`;
      }

      // 如果有分类路径，显示路径的最后一部分
      if (item.categoryPath) {
        const pathParts = item.categoryPath.split('/');
        const lastPart = pathParts[pathParts.length - 1];
        return `[${lastPart}]`;
      }

      // 兜底显示
      return '[未指定产品]';
    }
  }
};
</script>

<style scoped>
.defend-table {
  width: 100%;
}

.pagebreak {
  margin-top: 10px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.tableContainer {
  cursor: pointer;
}

.tableContainer:hover {
  color: #409EFF;
}
</style> 