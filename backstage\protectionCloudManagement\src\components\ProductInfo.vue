<template>
  <div>
    <el-dialog
      title="防护用品详细信息"
      :visible.sync="productInfoShow"
      :before-close="closeDialog"
      width="35%">
      <div slot="title">
        <div class="dialogTitle">防护用品详细信息</div>
        <el-divider></el-divider>     
      </div>
      <div class="boxOut">
        <div class="boxLeft">
          <div class="con">
            <div class="title1">产品名称: </div>
            <div class="content1">{{ productInfo.product }}</div>
          </div>
          <div class="con">
            <div class="title1">分类: </div>
            <div class="content1">{{ productInfo.categoryName || productInfo.categoryPath || productInfo.classification || '-' }}</div>
          </div>
          <div class="con">
            <div class="title1">产品规格: </div>
            <div class="content1">{{ productInfo.productSpec || '-' }}</div>
          </div>
          <div class="con">
            <div class="title1">物料编码: </div>
            <div class="content1">{{ productInfo.materialCode || '-' }}</div>
          </div>
          <div class="con">
            <div class="title1">使用方式: </div>
            <div class="content1">{{ productInfo.useMethod ? productInfo.useMethod : '-' }}</div>
          </div>
          <div class="con">
            <div class="title1">使用行业/环境: </div>
            <div class="content1">{{ productInfo.industryEnvironment === '/' ? "-" : productInfo.industryEnvironment }}</div>
          </div>
        </div>
        <div class="boxRight">
          <div class="con">
            <div class="title1">产品型号: </div>
            <div class="content1">{{ productInfo.modelNumber ? productInfo.modelNumber : '-' }}</div>
          </div>
          <div class="con">
            <div class="title1">防护类型: </div>
            <div class="content1">{{ productInfo.protectionType ? productInfo.protectionType : '-' }}</div>
          </div>
          <div class="con">
            <div class="title1">防护用途: </div>
            <div class="content1">{{ productInfo.function ? productInfo.function : '-' }}</div>
          </div>
          <div class="con">
            <div class="title1">危害因素: </div>
            <div class="content1">{{ productInfo.harmFactors && ( productInfo.harmFactors.length > 0 ) ? productInfo.harmFactors.join('、') : '-'}}</div>
          </div>
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="closeDialog">关 闭</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
  export default {
    props: {
      productInfo: Object,
    },

    data() {
      return {
        productInfoShow: true,
      }
    },

    methods: {
      closeDialog() {
        console.log(111, '组件里面点击');
        this.$emit("closeDialog");
      }
    }
  }
</script>

<style scoped>
.dialogTitle {
  font-size: 18px;
  font-weight: 600;
  margin-bottom: -10px;
}

.con {
  text-align: left;
  height: 32px;
  margin-bottom: 16px;
  display: flex;
  justify-content: flex-start;
}

.title1 {
  font-size: 16px;
  font-weight: 600;
  width: 34%;
  height: 32px;
  text-align: right;
  margin-right: 16px;
}

.content1 {
  height: 32px;
  line-height: 16px;
  white-space: normal;
  word-wrap: break-word;
}

.boxOut {
  display: flex;
  flex-direction: row;
  justify-content: space-around;
}

.boxLeft {
  flex: 1;
}

.boxRight {
  flex: 1;
}
</style>