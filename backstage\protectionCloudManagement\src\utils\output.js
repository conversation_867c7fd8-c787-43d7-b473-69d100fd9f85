import { exportByMonth } from '@/api';
import { exportReceiveRecords } from '@/api/defendManage.js';

const downloadExportFile = (blob, tagFileName) => {
  const downloadElement = document.createElement('a');
  let href = blob;
  if (typeof blob === 'string') {
    downloadElement.target = '_blank';
  } else {
    href = window.URL.createObjectURL(blob); // 创建下载的链接
  }
  downloadElement.href = href;
  downloadElement.download = tagFileName; // 下载后文件名
  document.body.appendChild(downloadElement);
  downloadElement.click(); // 点击下载
  document.body.removeChild(downloadElement); // 下载完成移除元素
  if (typeof blob !== 'string') {
    window.URL.revokeObjectURL(href); // 释放掉blob对象
  }
};

export const fileLinkToStreamDownload = (url, fileName, type) => {
  // eslint-disable-next-line no-undef
  console.log(fileName, 'fileName');
  // eslint-disable-next-line no-undef
  const xhr = new XMLHttpRequest();
  xhr.open('get', url, true);
  xhr.setRequestHeader('Content-Type', type);
  xhr.responseType = 'blob';
  xhr.onload = res => {
    if (res.currentTarget.status === 200) {
      // 接受二进制文件流
      const blob = res.currentTarget.response;
      const newfileName = decodeURIComponent(
        url.match(/\/([^\/?#]+\.docx)(?:[\/?#]|$)/i)[1]
      );
      downloadExportFile(blob, newfileName);
    }
  };
  xhr.send();
};

export const scrapOutputFn = (params, vueInstance = null) => {
  if (params.type === 'public' || params.type === 'personal') {
    exportReceiveRecords(params).then(result => {
      console.log(result, '记录111');
      if (result.data === '暂无报废数据或数据未审核通过！') {
        // 添加用户提示
        if (vueInstance && vueInstance.$message) {
          vueInstance.$message({
            message: '暂无报废数据或数据未审核通过！',
            type: 'warning',
          });
        }
        return;
      }
      const url = result.data;
      const array = result.data.split('/');
      const fileName = array[array.length - 1];
      const fileSplit = result.data.split('.');
      const fileType = fileSplit[fileSplit.length - 1];
      fileLinkToStreamDownload(url, fileName, fileType);

      // 添加成功提示
      if (vueInstance && vueInstance.$message) {
        vueInstance.$message({
          message: '导出成功！',
          type: 'success',
        });
      }
    });
  } else {
    exportByMonth(params).then(result => {
      console.log(result, '111111223213');
      if (result.data === '暂无报废数据或数据未审核通过！') {
        // 添加用户提示
        if (vueInstance && vueInstance.$message) {
          vueInstance.$message({
            message: '暂无报废数据或数据未审核通过！',
            type: 'warning',
          });
        }
        return;
      }
      const url = result.data;
      const array = result.data.split('/');
      const fileName = array[array.length - 1];
      const fileSplit = result.data.split('.');
      const fileType = fileSplit[fileSplit.length - 1];
      fileLinkToStreamDownload(url, fileName, fileType);

      // 添加成功提示
      if (vueInstance && vueInstance.$message) {
        vueInstance.$message({
          message: '导出成功！',
          type: 'success',
        });
      }
    });
  }
};
