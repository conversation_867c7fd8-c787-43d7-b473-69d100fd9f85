<template>
  <div style="text-align: left">
    <!-- 添加模式切换和搜索区域 -->
    <div class="search-area">
      <div class="switch-container">
        <span :class="['switch-label', searchMode === 'date' ? 'active' : '']">公司/装置台账</span>
        <el-switch
          v-model="searchMode"
          active-value="employee"
          inactive-value="date"
          @change="handleModeChange"
        ></el-switch>
        <span :class="['switch-label', searchMode === 'employee' ? 'active' : '']">员工台账</span>
      </div>
    </div>

    <!-- 查询条件区域 -->
    <div class="query-area">
      <!-- 公司台账查询条件 -->
      <template v-if="searchMode === 'date'">
        <el-tabs type="border-card" v-model="activeName">
          <el-tab-pane v-for="(item, i) in dateFilter" :key="i" :label="item" :name="i">
            <div>
              <span style="font-size: 14px;">年份：</span>
              <el-date-picker
                v-model="yearTime"
                type="year"
                clearable="false"
                placeholder="选择年份"
                size="mini"
                style="margin-right: 10px"
              >
              </el-date-picker>
              <el-button :disabled="!buttonShow" type="danger" size="mini" @click="dels" style="margin-bottom: 15px" >删除</el-button>
              <el-button type="primary" icon="el-icon-download" size="mini" @click="exportRecords('public')" style="margin-bottom: 15px" >导出</el-button>
            </div>
            <defend-table
              :table-data="tableData"
              :total="count"
              :show-pagination="pagebreak"
              @selection-change="handleSelectionChange"
              @delete="handleDelete"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
              @product-click="openInfo"
            />
          </el-tab-pane>
        </el-tabs>
      </template>

      <!-- 员工台账查询条件 -->
      <template v-else>
        <div class="employee-search">
          <treeselect
            v-model="employeeName"
            :noResultsText="noResultsText"
            :valueFormat="valueFormat"
            :multiple="false"
            :async="true"
            :options="employeeOptions"
            :normalizer="normalizer"
            :placeholder="employeeName?'请输入员工姓名':''"
            :disable-branch-nodes="true"
            :load-options="loadOptions"
            :placeholder-show-always="true"
            searchPromptText="请输入员工姓名"
            style="width: 300px;"
          >
            <div slot="value-label" slot-scope="{ node }">{{ node.raw.name ? node.raw.name : node.raw.label }}</div>
          </treeselect>
          <el-button type="primary" icon="el-icon-search" size="mini" @click="searchEmployee" style="margin-left: 10px" >搜索</el-button>
          <!-- <el-button :disabled="!buttonShow" type="danger" size="mini" @click="dels" style="margin-left: 10px" >删除</el-button>
          <el-button type="primary" icon="el-icon-download" size="mini" @click="exportRecords('public')" style="margin-left: 10px" >导出</el-button> -->
        </div>
        <defend-table
          :table-data="tableData"
          :total="count"
          :show-pagination="pagebreak"
          @selection-change="handleSelectionChange"
          @delete="handleDelete"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          @product-click="openInfo"
        />
      </template>
    </div>

    <ProductInfo v-if="productInfoShow" :productInfo="productInfo" @closeDialog="handleCloseDialog" />
  </div>
</template>

<script>
import { fillMixin } from "../../utils/fillMixin.js";
import moment from "moment";
import { mapGetters } from "vuex";
import Treeselect from '@riophae/vue-treeselect';
import '@riophae/vue-treeselect/dist/vue-treeselect.css';
import {
  getReceiveRecordList,
  delReceiveRecord,
  getEmployeeInfo,
} from "@/api/defendManage.js";
import ProductInfo from "../../components/ProductInfo";
import DefendTable from "../../components/DefendTable.vue";
import { scrapOutputFn } from '../../utils/output.js'
import XLSX from "xlsx";

function debounce(func, wait) {
  let timeout;
  return function(...args) {
    clearTimeout(timeout);
    timeout = setTimeout(() => {
      func.apply(this, args);
    }, wait);
  };
}

export default {
  props: {
    date: {
      type: String,
      default: ''
    },
    valueFormat: {
      type: String,
      default: 'object',
    },
    noResultsText: {
      type: String,
      default: '没有数据',
    },
  },
  components: {
    ProductInfo,
    DefendTable,
    Treeselect
  },
  data() {
    return {
      showButton: true,
      isEdit: {},
      pagebreak: true,
      currentPage: 1,
      count: 0,
      pageSize: 10,
      time: [],
      tableData: [],
      date: "",
      activeName: new Date().getMonth(),
      yearTime: "",
      dateFilter: [
        "1月",
        "2月",
        "3月",
        "4月",
        "5月",
        "6月",
        "7月",
        "8月",
        "9月",
        "10月",
        "11月",
        "12月",
      ],
      multipleSelection: [],
      buttonShow: false,
      filePath: "",
      path: "",
      productInfoShow: false,
      productInfo: {},
      searchMode: 'date',
      employeeName: '',
      employeeOptions: [],
      infoData: [],
      employeeSearched: false,
    };
  },
  watch: {
    async yearTime(year) {
      if (this.searchMode === 'date') {
        if (!year) {
          this.$message({
            type: "warning",
            message: "请选择年份!",
          });
          return;
        }
        this.yearTime = moment(year).format("YYYY");
        await this.fetch();
      }
    },
    async activeName(month) {
      await this.fetch();
    },
  },
  
  computed: {
    ...mapGetters(["defendProductList", "branch"]),
  },

  mixins: [fillMixin],
  methods: {
    //批量删除
    dels() {
      if (this.multipleSelection.length === 0) {
        this.$message.error("请勾选你想要删除的数据");
      } else {
        this.$confirm("确定要删除吗？", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        })
          .then(async () => {
            const ids = this.multipleSelection.map((item) => {
              return item._id;
            });
            await delReceiveRecord({ ids });
            this.$message({
              type: "success",
              message: "删除成功!",
            });
            this.fetch();
          })
          .catch(() => {
            this.$message({
              type: "info",
              message: "已取消删除",
            });
          });
      }
    },

    handleSelectionChange(selection) {
      this.buttonShow = selection.length > 0;
      this.multipleSelection = selection;
    },

    async handleDelete(item) {
      if (item._id) {
        this.$confirm("确定是否要删除数据?", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        })
          .then(async () => {
            await delReceiveRecord({
              ids: [item._id],
            });

            this.$message({
              type: "success",
              message: "删除成功!",
            });
            this.fetch();
          })
          .catch(() => {
            this.$message({
              type: "info",
              message: "已取消删除",
            });
          });
      }
    },

    handleSizeChange(val) {
      this.pageSize = val;
      this.fetch();
    },

    async handleCurrentChange(val) {
      this.currentPage = val;
      this.fetch();
    },

    async fetch() {
      // 日期模式需要年份检查
      if (this.searchMode === 'date' && !this.yearTime) {
        this.$message({
          type: "warning",
          message: "请选择年份!",
        });
        return;
      }
      
      // 如果是员工模式且未进行搜索，直接返回不查询数据
      if (this.searchMode === 'employee' && !this.employeeSearched) {
        return;
      }
      
      // 构建查询参数
      let params = {
        current: this.currentPage,
        pageSize: this.pageSize,
      };

      if (this.searchMode === 'date') {
        // 日期模式查询参数
        const min = `${this.yearTime}-${
          this.activeName >= 9 || this.activeName == 0
            ? this.activeName + 1
            : "0" + (this.activeName + 1)
        }`;
        let max = `${this.yearTime}-${
          this.activeName >= 8 || this.activeName == 0
            ? this.activeName + 2
            : "0" + (this.activeName + 2)
        }`;
        if (this.activeName == 11) {
          max = (Number(this.yearTime) + 1) + "-01-01";
        }
        params.startTime = moment(min);
        params.endTime = moment(max);
      } else {
        // 员工模式查询参数
        params.employee = this.employeeName.employeeId;
      }

      try {
        const res = await getReceiveRecordList(params);

        if (res.data.tableData) {
          this.tableData = res.data.tableData;
          this.count = res.data.total;
        }
      } catch (error) {
        console.error('获取数据失败:', error);
        this.$message.error('获取数据失败，请稍后重试');
      }
    },

    async changetime() {
      this.yearTime = new Date().getFullYear() + "";
    },

    handleCloseDialog() {
      this.productInfoShow = false;
    },

    // 导出记录
    async exportRecords(type) {
      scrapOutputFn({
        time: this.yearTime,
        type,
      }, this) // 传递Vue实例以便显示提示
    },
    
    // 打开详细信息
    openInfo(row) {
      this.productInfoShow = true;
      this.defendProductList.forEach(product => {
        if (product._id === row.productIds[0]) {
          product.data.forEach(item => {
            if (item._id === row.productIds[1]) {
              this.productInfo = item;
            }
          })
        }
      })
    },

    handleModeChange() {
      // 清空表格数据和相关状态
      this.tableData = [];
      this.count = 0;
      
      if (this.searchMode === 'date') {
        // 切换到日期模式
        this.activeName = new Date().getMonth();
        this.yearTime = new Date().getFullYear() + ""; // 设置为当年
        this.fetch(); // 自动加载日期模式的数据
      } else {
        // 切换到员工模式
        this.activeName = new Date().getMonth();
        this.yearTime = '';
        this.employeeName = '';
        this.employeeSearched = false;
        // 员工模式下不自动加载数据
      }
    },

    normalizer(node) {
      const id = node.employeeId || node.id || ("node-" + Math.random().toString(36).substr(2, 9));
      
      if (node.employeeId) {
        return {
          id: id,
          label: node.name || '',
          raw: node,
        }
      } else { 
        return {
          id: id,
          label: node.name || '',
          isDefaultExpanded: true,
          children: node.children || [],
          raw: node,
        };
      }
    },

    loadOptions: debounce(function({ action, parentNode, callback, searchQuery }) {
      if (searchQuery.length < 2) {
        this.noResultsText = '请输入不少于2个字符';
        callback(null, []);
        return;
      }
      getEmployeeInfo({ 
        searchName: searchQuery, 
        loadingConfig: {
          isLoadingMaskDisable: true,
        } 
      }).then((res) => {
        if (res.status === 200) {
          this.infoData = res.data;
          if(res.data.length === 0) {
            this.noResultsText = '没有数据';
          }
          callback(null, res.data);
        }
      }).catch(err => {
        callback(err, null);
      });
    }, 300),

    // 添加搜索员工方法
    searchEmployee() {
      if (!this.employeeName) {
        this.$message.warning('请先选择员工');
        return;
      }
      this.employeeSearched = true;
      this.fetch();
    },



    // 加载岗位选项（根级别）
    async loadStationOptions() {
      try {
        console.log('开始加载根级别岗位选项...');
        // 使用懒加载方式，初始只加载根级别
        const res = await this.$http.get('/manage/millConstruction/findMillConstructionLazy', {
          params: { showEmployee: false }
        });

        console.log('根级别岗位选项API返回:', res);

        if (res.data && res.data.status === 200) {
          const data = res.data.data || [];
          console.log('根级别原始数据:', data);

          // 转换为级联选择器格式
          this.stationOptions = data.map(item => ({
            _id: item._id,
            fullId: item.fullId,
            name: item.name,
            category: item.category,
            hasChildren: item.hasChildren,
            leaf: item.category === 'stations' // 岗位是叶子节点
          }));

          console.log('根级别构建后的选项:', this.stationOptions);
        } else {
          console.error('根级别岗位选项API返回错误:', res);
          this.$message.error('加载岗位选项失败: ' + (res.data && (res.data.message || res.data.msg) || '未知错误'));
        }
      } catch (error) {
        console.error('加载根级别岗位选项失败:', error);
        this.$message.error('加载岗位选项失败: ' + error.message);
      }
    },











    // 获取进度条颜色
    getProgressColor(percentage) {
      if (percentage >= 80) return '#67C23A';
      if (percentage >= 60) return '#E6A23C';
      if (percentage >= 40) return '#F56C6C';
      return '#F56C6C';
    },

    // 查看员工详情
    viewEmployeeDetail(employee) {
      // 这里可以打开一个详情弹窗显示员工的详细领用记录
      console.log('查看员工详情:', employee);

      // 可以复用现有的产品信息弹窗或创建新的员工详情弹窗
      this.$message.info("查看 " + employee.employeeName + " 的详细领用记录");
    },

    // 格式化日期
    formatDate(date) {
      if (!date) return '-';
      const d = new Date(date);
      return (d.getMonth() + 1) + "/" + d.getDate();
    },
  },

  async mounted() {
    if (this.date) {
      this.yearTime = this.date.split('-')[0];
      this.activeName = this.date.split('-')[1] - 1;
    }
    this.changetime();
    // 仅在日期模式下初始加载数据
    if (this.searchMode === 'date') {
      this.fetch();
    }
  },
};
</script>

<style scoped>
element.style {
  z-index: 2007;
}
.el-date-editor.el-input {
  width: 135px;
}
.pagebreak {
  margin-top: 10px;
  display: flex;
  justify-content: center;
  align-items: center;
}
body .el-table th.gutter {
  display: table-cell !important;
}
body .el-table colgroup.gutter {
  display: table-cell !important;
}
.el-range-editor.el-input__inner {
  position: absolute;
  right: 90px;
}
#form {
  display: inline-block;
  width: 350px;
}
#letter {
  position: absolute;
  right: 400px;
}
#creatTime {
  /* display: inline-block; */
  width: 200;
  position: absolute;
  top: 150px;
  right: 100px;
  /* bottom: 10px; */
  line-height: 30px;
}
#excel {
  position: absolute;
  top: 3px;
  right: 50px;
}
#save {
  position: absolute;
  top: 3px;
  right: 0;
}
.el-input__inner {
  width: 83%;
}

.box-card {
  width: 98%;
  height: 100%;
  margin: 0 auto;
}

.font-small {
  font-size: 14px;
  color: rgb(200, 208, 214);
  margin: 0 auto;
  text-align: center;
}

.corss-line {
  margin-top: 30px;
  border: 1px solid rgb(200, 208, 214);
}

.tableContainer {
  cursor: pointer;
}

.tableContainer:hover {
  color: #409EFF;
}

.buttons {
  margin-top: 30px;
  text-align: center;
}

.uploadImage {
  width: 90%;
}

.template {
  position: relative;
  display: flex;
  /* margin-top: 20px; */
  margin-bottom: 20px;
}

.search-area {
  margin-bottom: 20px;
  display: flex;
  align-items: center;
}

.employee-search {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

.switch-container {
  display: flex;
  align-items: center;
  gap: 10px;
}

.switch-label {
  font-size: 14px;
  color: #606266;
  cursor: pointer;
}

/* .switch-label.active {
  color: #409EFF;
  font-weight: bold;
} */

 .active {
  background-color: white;
 }

.query-area {
  margin-bottom: 20px;
}
:deep(.vue-treeselect__control) {
  height: 32px;
}

/* 视图模式切换样式 */
.view-mode-switch {
  display: flex;
  align-items: center;
  gap: 10px;
}

.view-mode-switch .el-radio-group {
  background: #f5f7fa;
  border-radius: 4px;
  padding: 4px;
}



.station-filters {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 10px;
  padding: 15px;
  background: #f8f9fa;
  border-radius: 4px;
  border: 1px solid #e9ecef;
}

.station-summary {
  margin-bottom: 20px;
}

.station-summary .el-card {
  border: 1px solid #e4e7ed;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
}

/* 状态标签样式增强 */
.el-tag.el-tag--success {
  background-color: #f0f9ff;
  border-color: #67c23a;
  color: #67c23a;
}

.el-tag.el-tag--warning {
  background-color: #fdf6ec;
  border-color: #e6a23c;
  color: #e6a23c;
}

.el-tag.el-tag--danger {
  background-color: #fef0f0;
  border-color: #f56c6c;
  color: #f56c6c;
}

.el-tag.el-tag--info {
  background-color: #f4f4f5;
  border-color: #909399;
  color: #909399;
}



/* 状态说明提示框样式 */
.status-help {
  max-width: 300px;
  line-height: 1.6;
}

.status-item {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
  padding: 4px 0;
}

.status-item:last-child {
  margin-bottom: 0;
}

.status-item .el-tag {
  margin-right: 10px;
  min-width: 80px;
  text-align: center;
  flex-shrink: 0;
}

.status-item span {
  color: #606266;
  font-size: 13px;
  flex: 1;
}
</style>

<!-- 全局样式：状态提示框 -->
<style>
.status-tooltip {
  max-width: 350px !important;
}

.status-tooltip .el-tooltip__popper {
  padding: 15px !important;
  background: #ffffff !important;
  border: 1px solid #e4e7ed !important;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1) !important;
  color: #606266 !important;
  border-radius: 4px !important;
}

.status-tooltip .el-tooltip__popper[x-placement^="top"] .el-tooltip__arrow {
  border-top-color: #e4e7ed !important;
}

.status-tooltip .el-tooltip__popper[x-placement^="top"] .el-tooltip__arrow::after {
  border-top-color: #ffffff !important;
}

.status-tooltip .el-tooltip__popper[x-placement^="bottom"] .el-tooltip__arrow {
  border-bottom-color: #e4e7ed !important;
}

.status-tooltip .el-tooltip__popper[x-placement^="bottom"] .el-tooltip__arrow::after {
  border-bottom-color: #ffffff !important;
}

.status-tooltip .el-tooltip__popper[x-placement^="left"] .el-tooltip__arrow {
  border-left-color: #e4e7ed !important;
}

.status-tooltip .el-tooltip__popper[x-placement^="left"] .el-tooltip__arrow::after {
  border-left-color: #ffffff !important;
}

.status-tooltip .el-tooltip__popper[x-placement^="right"] .el-tooltip__arrow {
  border-right-color: #e4e7ed !important;
}

.status-tooltip .el-tooltip__popper[x-placement^="right"] .el-tooltip__arrow::after {
  border-right-color: #ffffff !important;
}
</style>
