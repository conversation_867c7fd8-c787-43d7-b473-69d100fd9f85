<template>
  <div class="station-view">
    <!-- 筛选条件 -->
    <div class="station-filters" style="margin-bottom: 20px;">
      <el-date-picker
        v-model="selectedYear"
        type="year"
        placeholder="选择年份"
        size="small"
        style="width: 120px; margin-right: 10px;"
        @change="handleYearChange"
        :value="new Date()"
      />
      <el-select
        v-model="selectedMonth"
        placeholder="选择月份"
        size="small"
        style="width: 100px; margin-right: 10px;"
        @change="handleMonthChange"
      >
        <el-option
          v-for="(month, index) in dateFilter"
          :key="index"
          :label="month"
          :value="index + 1"
        />
      </el-select>
      <el-cascader
        v-model="selectedWorkUnit"
        :options="workUnitOptions"
        :props="cascaderPropsComputed"
        placeholder="选择工作单元"
        size="small"
        style="width: 300px; margin-right: 10px;"
        filterable
        clearable
        @change="handleWorkUnitChange"
      />
      <el-select
        v-model="configStatusFilter"
        placeholder="配发标准状态"
        size="small"
        style="width: 150px; margin-right: 10px;"
        clearable
        @change="handleConfigStatusChange"
      >
        <el-option
          v-for="status in configStatusOptions"
          :key="status.value"
          :label="status.label"
          :value="status.value"
        />
      </el-select>
      <el-input
        v-model="searchKeyword"
        placeholder="搜索工作场所"
        size="small"
        style="width: 200px; margin-right: 10px;"
        clearable
        @keyup.enter.native="handleSearch"
      >
        <i slot="prefix" class="el-input__icon el-icon-search"></i>
      </el-input>
      <el-button
        type="primary"
        size="small"
        icon="el-icon-search"
        @click="handleSearch"
      >
        查询
      </el-button>
    </div>

    <!-- 统计信息 -->
    <div v-if="stationSummary.total > 0" class="station-summary" style="margin-bottom: 20px;">
      <el-card>
        <div style="display: flex; align-items: center; gap: 20px;">
          <span><strong>{{ stationSummary.stationName }}</strong></span>
          <span>总人数: {{ stationSummary.total }}</span>
          <el-tag type="success" size="small">已完成: {{ stationSummary.completed }}</el-tag>
          <el-tag type="warning" size="small">未完整: {{ stationSummary.partial }}</el-tag>
          <el-tag type="danger" size="small">未领用: {{ stationSummary.pending }}</el-tag>
          <el-tag type="info" size="small">已拒绝: {{ stationSummary.rejected }}</el-tag>
          <el-tag style="background-color: #E6A23C; color: white;" size="small">即将到期: {{ stationSummary.expiring }}</el-tag>
        </div>
      </el-card>
    </div>

    <!-- 岗位汇总列表表格 -->
    <el-table
      :data="stationListData"
      :loading="stationLoading"
      stripe
      border
      style="width: 100%;"
      :header-cell-style="{background:'#F5F7FA',color:'#4C91E9', 'font-weight':'normal'}"
    >
      <el-table-column prop="enterpriseName" label="公司" width="200" />
      <el-table-column prop="fullPath" label="完整路径" min-width="250" />
      <el-table-column label="总人数" width="100" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.totalEmployeeCount }}</span>
        </template>
      </el-table-column>
      <el-table-column label="领用完成率" width="120" align="center">
        <template slot-scope="scope">
          <el-progress
            :percentage="scope.row.completionRate"
            :color="getProgressColor(scope.row.completionRate)"
            :stroke-width="10"
          />
        </template>
      </el-table-column>
      <el-table-column min-width="300">
        <template slot="header">
          <span>领用状态分布</span>
          <el-tooltip effect="light" placement="top" popper-class="status-tooltip">
            <div slot="content">
              <div class="status-help">
                <div class="status-item">
                  <el-tag type="warning" size="mini">待领取</el-tag>
                  <span>系统已生成领用记录，等待员工确认</span>
                </div>
                <div class="status-item">
                  <el-tag type="danger" size="mini">未领用</el-tag>
                  <span>系统还没有生成领用记录</span>
                </div>
                <div class="status-item">
                  <el-tag type="success" size="mini">已领用完整</el-tag>
                  <span>员工已确认领取所有防护用品</span>
                </div>
                <div class="status-item">
                  <el-tag type="warning" size="mini">未领用完整</el-tag>
                  <span>员工只领取了部分防护用品</span>
                </div>
                <div class="status-item">
                  <el-tag type="info" size="mini">拒绝领用</el-tag>
                  <span>员工主动拒绝领取</span>
                </div>
                <div class="status-item">
                  <el-tag type="warning" size="mini">即将到期</el-tag>
                  <span>已领用但即将到期需要重新领取</span>
                </div>
              </div>
            </div>
            <i class="el-icon-question" style="margin-left: 5px; color: #409EFF; cursor: pointer;"></i>
          </el-tooltip>
        </template>
        <template slot-scope="scope">
          <div class="status-distribution">
            <el-tag v-if="scope.row.summary.completed > 0" type="success" size="mini" style="margin: 2px;">
              已完成: {{ scope.row.summary.completed }}
            </el-tag>
            <el-tag v-if="scope.row.summary.partial > 0" type="warning" size="mini" style="margin: 2px;">
              部分完成: {{ scope.row.summary.partial }}
            </el-tag>
            <el-tag v-if="scope.row.summary.pending > 0" type="danger" size="mini" style="margin: 2px;">
              未领用: {{ scope.row.summary.pending }}
            </el-tag>
            <el-tag v-if="scope.row.summary.rejected > 0" type="info" size="mini" style="margin: 2px;">
              拒绝: {{ scope.row.summary.rejected }}
            </el-tag>
            <el-tag v-if="scope.row.summary.expiring > 0" style="background-color: #E6A23C; color: white; margin: 2px;" size="mini">
              即将到期: {{ scope.row.summary.expiring }}
            </el-tag>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="配发标准" width="120" align="center">
        <template slot-scope="scope">
          <el-tag
            v-if="scope.row.configStatus === 'configured'"
            type="success"
            size="mini"
          >
            已配置
          </el-tag>
          <el-tag
            v-else-if="scope.row.configStatus === 'no_need'"
            type="info"
            size="mini"
          >
            无需配置
          </el-tag>
          <el-tag
            v-else
            type="danger"
            size="mini"
          >
            未配置
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="120" align="center">
        <template slot-scope="scope">
          <el-button
            type="primary"
            size="mini"
            @click="viewStationDetail(scope.row)"
          >
            查看详情
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <div style="margin-top: 20px; text-align: right;">
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="pagination.currentPage"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="pagination.pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="pagination.total"
      />
    </div>

    <!-- 岗位详情对话框 -->
    <el-dialog
      title="岗位员工详情"
      :visible.sync="stationDetailVisible"
      width="90%"
      :before-close="() => { stationDetailVisible = false; }"
    >
      <div v-if="currentStationInfo">
        <h3>{{ currentStationInfo.name }} - 员工领用详情</h3>
        <p>路径：{{ currentStationInfo.fullPath }}</p>
        <p>总人数：{{ currentStationInfo.totalEmployeeCount }}</p>

        <!-- 员工详情表格 -->
        <el-table
          :data="stationEmployeeData"
          stripe
          border
          style="width: 100%; margin-top: 20px;"
        >
          <el-table-column prop="employeeName" label="员工姓名" width="120" />
          <el-table-column width="120" align="center">
            <template slot="header">
              <span>领用状态</span>
              <el-tooltip effect="light" placement="top" popper-class="status-tooltip">
                <div slot="content">
                  <div class="status-help">
                    <div class="status-item">
                      <el-tag type="warning" size="mini">待领取</el-tag>
                      <span>系统已生成领用记录，等待员工确认</span>
                    </div>
                    <div class="status-item">
                      <el-tag type="danger" size="mini">未领用</el-tag>
                      <span>系统还没有生成领用记录</span>
                    </div>
                    <div class="status-item">
                      <el-tag type="success" size="mini">已领用完整</el-tag>
                      <span>员工已确认领取所有防护用品</span>
                    </div>
                    <div class="status-item">
                      <el-tag type="warning" size="mini">未领用完整</el-tag>
                      <span>员工只领取了部分防护用品</span>
                    </div>
                    <div class="status-item">
                      <el-tag type="info" size="mini">拒绝领用</el-tag>
                      <span>员工主动拒绝领取</span>
                    </div>
                    <div class="status-item">
                      <el-tag type="warning" size="mini">即将到期</el-tag>
                      <span>已领用但即将到期需要重新领取</span>
                    </div>
                  </div>
                </div>
                <i class="el-icon-question" style="margin-left: 5px; color: #409EFF; cursor: pointer;"></i>
              </el-tooltip>
            </template>
            <template slot-scope="scope">
              <el-tag
                :type="scope.row.statusColor"
                size="small"
              >
                {{ scope.row.statusLabel }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="应配发防护用品" min-width="200">
            <template slot-scope="scope">
              <div v-if="scope.row.requiredProducts && scope.row.requiredProducts.length > 0">
                <el-tag
                  v-for="product in scope.row.requiredProducts"
                  :key="product._id"
                  size="mini"
                  style="margin: 2px;"
                >
                  {{ product.product }}{{ product.modelNumber ? `(${product.modelNumber})` : '' }} × {{ product.number }}
                </el-tag>
              </div>
              <span v-else style="color: #999;">暂无配发标准</span>
            </template>
          </el-table-column>
          <el-table-column label="已领用防护用品" min-width="200">
            <template slot-scope="scope">
              <div v-if="scope.row.receivedProducts && scope.row.receivedProducts.length > 0">
                <el-tag
                  v-for="product in scope.row.receivedProducts"
                  :key="product.product + '_' + (product.modelNumber || '')"
                  type="success"
                  size="mini"
                  style="margin: 2px;"
                >
                  {{ product.product }}{{ product.modelNumber ? `(${product.modelNumber})` : '' }} × {{ product.number }}
                </el-tag>
              </div>
              <span v-else style="color: #999;">暂无领用记录</span>
            </template>
          </el-table-column>
          <el-table-column label="最后领用时间" width="150" align="center">
            <template slot-scope="scope">
              <span v-if="scope.row.lastReceiveDate">
                {{ formatDate(scope.row.lastReceiveDate) }}
              </span>
              <span v-else style="color: #999;">-</span>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { fillMixin } from "../../utils/fillMixin.js";
import moment from "moment";
import { mapGetters } from "vuex";
import {
  getStationSummaryReceiveStatus,
  getStationDetailReceiveStatus,
} from "@/api/defendManage.js";

export default {
  name: 'DefendManageByStation',
  props: {
    date: String
  },
  mixins: [fillMixin],
  data() {
    return {
      // 筛选条件
      selectedYear: new Date().getFullYear(),
      selectedMonth: new Date().getMonth() + 1,
      selectedWorkUnit: '',
      configStatusFilter: '',
      searchKeyword: '', // 搜索关键词

      // 工作单元选项
      workUnitOptions: [],

      // 配发标准状态选项
      configStatusOptions: [
        { label: '已配置', value: 'configured' },
        { label: '未配置', value: 'not_configured' },
        { label: '无需配置', value: 'no_need' }
      ],

      // 岗位数据
      stationListData: [],
      stationLoading: false,
      stationSummary: {
        total: 0,
        completed: 0,
        partial: 0,
        pending: 0,
        rejected: 0,
        expiring: 0,
        stationName: ''
      },

      // 分页数据
      pagination: {
        currentPage: 1,
        pageSize: 20,
        total: 0
      },

      // 岗位详情对话框
      stationDetailVisible: false,
      currentStationInfo: null,
      stationEmployeeData: [],

      // 级联选择器配置
      cascaderProps: {
        lazy: true,
        checkStrictly: true, // 允许选择任意级别
        emitPath: false, // 只返回最后一级的值
        value: 'value',
        label: 'label',
        children: 'children',
        leaf: 'isLeaf'
      },

      // 月份选项
      dateFilter: [
        "1月",
        "2月",
        "3月",
        "4月",
        "5月",
        "6月",
        "7月",
        "8月",
        "9月",
        "10月",
        "11月",
        "12月"
      ],
    };
  },
  computed: {
    ...mapGetters(["branch"]),

    cascaderPropsComputed() {
      return {
        ...this.cascaderProps,
        lazyLoad: this.loadCascaderData
      };
    }
  },
  methods: {
    // 年份变化处理
    handleYearChange(value) {
      // value是Date对象，直接使用
      this.selectedYear = value || new Date();
      this.clearStationData();
    },

    // 月份变化处理
    handleMonthChange(value) {
      this.selectedMonth = value;
      this.clearStationData();
    },

    // 工作单元变化处理
    handleWorkUnitChange(value) {
      this.selectedWorkUnit = value;
      this.clearStationData();
    },

    // 配发标准状态变化处理
    handleConfigStatusChange(value) {
      this.configStatusFilter = value;
    },

    // 清空岗位数据
    clearStationData() {
      this.stationListData = [];
      this.stationSummary = {
        total: 0,
        completed: 0,
        partial: 0,
        pending: 0,
        rejected: 0,
        expiring: 0,
        stationName: ''
      };
    },

    // 搜索按钮点击事件（参考防护用品配发标准的实现）
    handleSearch() {
      console.log('=== handleSearch 方法被调用 ===');
      console.log('当前工作单元:', this.selectedWorkUnit);
      console.log('当前搜索关键词:', this.searchKeyword);
      console.log('按钮是否禁用:', !this.selectedWorkUnit && (!this.searchKeyword || !this.searchKeyword.trim()));
      // 重置到第一页
      this.pagination.currentPage = 1;
      this.searchStationEmployees();
    },

    // 处理页面大小变化
    handleSizeChange(val) {
      this.pagination.pageSize = val;
      this.pagination.currentPage = 1;
      this.searchStationEmployees();
    },

    // 处理当前页变化
    handleCurrentChange(val) {
      this.pagination.currentPage = val;
      this.searchStationEmployees();
    },

    // 搜索岗位员工
    async searchStationEmployees() {
      console.log('searchStationEmployees 方法被调用');
      console.log('当前选择的工作单元:', this.selectedWorkUnit);
      console.log('当前年份:', this.selectedYear);
      console.log('当前月份:', this.selectedMonth);
      console.log('当前搜索关键词:', this.searchKeyword);

      // 如果既没有工作单元也没有搜索关键词，且不是页面初始化，提示用户
      if (!this.selectedWorkUnit && (!this.searchKeyword || !this.searchKeyword.trim())) {
        // 页面初始化时允许空参数查询
        console.log('没有工作单元和搜索关键词，但继续执行查询（可能是页面初始化）');
      }

      this.stationLoading = true;

      const requestParams = {
        year: this.selectedYear instanceof Date ? this.selectedYear.getFullYear() : parseInt(this.selectedYear),
        month: this.selectedMonth,
        workUnitId: this.selectedWorkUnit, // 直接使用选择的工作单元ID，可以为空
        configStatus: this.configStatusFilter, // 添加配发标准状态筛选
        keyword: this.searchKeyword, // 添加关键词搜索参数
        page: this.pagination.currentPage, // 添加分页参数
        pageSize: this.pagination.pageSize // 添加每页数量参数
      };

      console.log('发送请求参数:', requestParams);

      try {
        console.log('开始调用API...');

        // 调用岗位汇总API
        const res = await getStationSummaryReceiveStatus(requestParams);

        console.log('接口返回结果:', res);

        // 检查响应格式
        if (res && res.status === 200) {
          console.log('响应数据结构:', res.data);

          const data = res.data;
          console.log('业务数据:', data);

          this.stationListData = data.stationSummaries || [];

          // 更新分页信息
          if (data.pagination) {
            this.pagination.total = data.pagination.total;
            this.pagination.currentPage = data.pagination.page;
            this.pagination.pageSize = data.pagination.pageSize;
          } else {
            // 兼容旧格式
            this.pagination.total = data.totalStations || this.stationListData.length;
          }

          console.log('设置的岗位汇总数据:', this.stationListData);
          console.log('分页信息:', this.pagination);

          if (this.stationListData.length === 0) {
            this.$message.info('该工作单元下暂无岗位数据');
          } else {
            this.$message.success(`成功加载 ${this.stationListData.length} 个岗位的数据，共 ${this.pagination.total} 个岗位`);
          }
        } else {
          console.error('响应格式错误:', res);
          this.$message.error(res.message || '获取岗位汇总数据失败');
        }
      } catch (error) {
        console.error('获取岗位汇总数据失败:', error);
        this.$message.error('获取岗位汇总数据失败');
      } finally {
        this.stationLoading = false;
      }
    },

    // 查看岗位详情
    async viewStationDetail(station) {
      console.log('查看岗位详情:', station);

      try {
        this.stationLoading = true;

        const requestParams = {
          stationId: station.stationId,
          year: this.selectedYear instanceof Date ? this.selectedYear.getFullYear() : parseInt(this.selectedYear),
          month: this.selectedMonth
        };

        console.log('获取岗位详情参数:', requestParams);

        const res = await getStationDetailReceiveStatus(requestParams);

        if (res && res.status === 200) {
          const data = res.data;

          // 设置当前岗位信息和员工数据
          this.currentStationInfo = data.stationInfo;
          this.stationEmployeeData = data.employees || [];
          this.stationSummary = data.summary || {};

          // 显示详情对话框
          this.stationDetailVisible = true;

          console.log('岗位详情数据:', data);
        } else {
          this.$message.error(res.message || '获取岗位详情失败');
        }
      } catch (error) {
        console.error('获取岗位详情失败:', error);
        this.$message.error('获取岗位详情失败: ' + error.message);
      } finally {
        this.stationLoading = false;
      }
    },

    // 获取进度条颜色
    getProgressColor(percentage) {
      if (percentage >= 80) return '#67C23A';
      if (percentage >= 60) return '#E6A23C';
      return '#F56C6C';
    },

    // 格式化日期
    formatDate(date) {
      if (!date) return '';
      return moment(date).format('YYYY-MM-DD HH:mm');
    },

    // 级联选择器懒加载数据
    async loadCascaderData(node, resolve) {
      const { level, value } = node;

      console.log('懒加载调试信息:', {
        level,
        value,
        node: node,
        data: node.data
      });

      try {
        const params = {
          level: level,
          pageSize: 100
        };

        // 如果不是根级别，添加parentId
        if (level > 0 && value) {
          params.parentId = value;
        }

        console.log('API请求参数:', params);

        const response = await this.$http.get('/api/workunit/cascade', {
          params: params
        });

        console.log('API响应:', response);

        if (response.data && response.data.code === 200) {
          const data = response.data.data || [];
          console.log('返回的数据:', data);
          resolve(data);
        } else {
          console.error('API返回错误:', response.data);
          resolve([]);
        }
      } catch (error) {
        console.error('加载级联数据失败:', error);
        resolve([]);
      }
    },

    // 加载工作单元选项
    async loadWorkUnitOptions() {
      // 使用级联选择器的懒加载，不需要预加载数据
      this.workUnitOptions = [];
    },

    // 页面初始化时加载数据
    async loadInitialData() {
      try {
        console.log('=== 页面初始化开始 ===');
        console.log('当前年份:', this.selectedYear);
        console.log('当前月份:', this.selectedMonth);

        // 页面初始化时直接加载数据，不依赖企业ID
        // 后端会根据年份和月份返回一些示例数据
        console.log('页面初始化 - 使用默认年份月份加载数据');
        await this.searchStationEmployees();
      } catch (error) {
        console.error('页面初始化失败:', error);
        // 初始化失败不影响页面正常使用
      }
    }
  },

  async created() {
    // 初始化年份和月份
    if (this.date) {
      const dateObj = new Date(this.date);
      this.selectedYear = new Date(dateObj.getFullYear(), 0, 1); // 设置为Date对象
      this.selectedMonth = dateObj.getMonth() + 1;
    } else {
      // 默认设置为当年
      this.selectedYear = new Date();
    }

    // 加载工作单元选项
    await this.loadWorkUnitOptions();

    // 页面初始化时自动加载当前企业的数据
    await this.loadInitialData();
  }
};
</script>

<style scoped lang="scss">
.station-view {
  padding: 20px;
}

.station-filters {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 10px;
}

.station-summary {
  .el-card {
    border: 1px solid #EBEEF5;
  }
}

.status-distribution {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.status-help {
  .status-item {
    display: flex;
    align-items: center;
    margin-bottom: 8px;

    .el-tag {
      margin-right: 8px;
      min-width: 80px;
    }

    span {
      font-size: 12px;
      color: #666;
    }
  }
}

.status-tooltip {
  max-width: 300px;
}
</style>
