/* eslint-disable no-unused-vars */
/*
 * @Author: 系统重构
 * @Date: 2024-01-01
 * @Description: 防护用品配发标准导入服务
 * 支持Excel模板生成、数据解析、验证和批量导入
 */

const Service = require('egg').Service;
const XLSX = require('xlsx');
const ExcelJS = require('exceljs');
const shortid = require('shortid');

class ProtectionStandardImportService extends Service {

  /**
   * 生成配发标准导入模板
   * @returns {Buffer} Excel文件Buffer
   */
  async generateImportTemplate() {
    const { ctx } = this;

    try {
      // 获取下拉选择数据
      const dropdownData = await this.getDropdownData();

      // 使用ExcelJS生成带下拉选择的Excel模板
      const workbook = new ExcelJS.Workbook();
      const worksheet = workbook.addWorksheet('配发标准导入模板');

      // 设置表头
      const headers = [
        { key: 'workTypePath', label: '工种路径', width: 40 },
        { key: 'categoryPath', label: '防护用品分类', width: 30 },
        { key: 'quantity', label: '数量', width: 10 },
        { key: 'cycle', label: '周期', width: 10 },
        { key: 'cycleUnit', label: '单位', width: 10 },
        { key: 'remark', label: '备注', width: 20 },
      ];

      // 添加表头行
      const headerRow = worksheet.addRow(headers.map(h => h.label));
      headerRow.font = { bold: true };
      headerRow.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: 'FFE0E0E0' },
      };

      // 设置列宽
      headers.forEach((header, index) => {
        worksheet.getColumn(index + 1).width = header.width;
      });

      // 动态生成示例数据
      const sampleData = this.generateSampleData(dropdownData);

      // 添加示例数据行
      sampleData.forEach(rowData => {
        worksheet.addRow(rowData);
      });

      // 合并单元格（工种路径）
      worksheet.mergeCells('A2:A4'); // 第一个工种的路径
      worksheet.mergeCells('A5:A6'); // 第二个工种的路径

      // 创建隐藏的数据工作表
      const dataSheet = workbook.addWorksheet('数据源');
      dataSheet.state = 'hidden';

      // 添加工种路径数据到隐藏工作表（包含下拉选择）
      dropdownData.workTypePaths.forEach((path, index) => {
        dataSheet.getCell(`A${index + 1}`).value = path;
      });

      // 添加防护用品分类数据到隐藏工作表
      dropdownData.categoryPaths.forEach((path, index) => {
        dataSheet.getCell(`B${index + 1}`).value = path;
      });

      // 添加周期单位数据到隐藏工作表
      dropdownData.cycleUnits.forEach((unit, index) => {
        dataSheet.getCell(`C${index + 1}`).value = unit;
      });

      // 设置数据验证（下拉选择）
      const maxRows = 1000;

      // 工种路径下拉 (A列)
      for (let row = 2; row <= maxRows + 1; row++) {
        worksheet.getCell(`A${row}`).dataValidation = {
          type: 'list',
          allowBlank: true,
          formulae: [ `数据源!$A$1:$A$${dropdownData.workTypePaths.length}` ],
        };
      }

      // 防护用品分类下拉 (B列)
      if (dropdownData.categoryPaths.length > 0) {
        for (let row = 2; row <= maxRows + 1; row++) {
          worksheet.getCell(`B${row}`).dataValidation = {
            type: 'list',
            allowBlank: true,
            formulae: [ `数据源!$B$1:$B$${dropdownData.categoryPaths.length}` ],
          };
        }
      }

      // 周期单位下拉 (E列)
      for (let row = 2; row <= maxRows + 1; row++) {
        worksheet.getCell(`E${row}`).dataValidation = {
          type: 'list',
          allowBlank: true,
          formulae: [ `数据源!$C$1:$C$${dropdownData.cycleUnits.length}` ],
        };
      }

      // 数量和周期列设置数字验证
      for (let row = 2; row <= maxRows + 1; row++) {
        // 数量验证（正整数）
        worksheet.getCell(`C${row}`).dataValidation = {
          type: 'whole',
          operator: 'greaterThan',
          formulae: [ 0 ],
          errorTitle: '数据验证错误',
          error: '数量必须为正整数',
        };

        // 周期验证（正整数）
        worksheet.getCell(`D${row}`).dataValidation = {
          type: 'whole',
          operator: 'greaterThan',
          formulae: [ 0 ],
          errorTitle: '数据验证错误',
          error: '周期必须为正整数',
        };
      }

      // 生成Excel文件Buffer
      const buffer = await workbook.xlsx.writeBuffer();
      return buffer;

    } catch (error) {
      ctx.logger.error('生成配发标准导入模板失败:', error);
      throw new Error(`生成导入模板失败: ${error.message}`);
    }
  }

  /**
   * 获取下拉选择数据
   * @returns {Object} 下拉数据
   */
  async getDropdownData() {
    const { ctx } = this;

    try {
      // 获取用户有权限的所有企业ID
      const userEnterpriseIds = await this.getUserEnterpriseIds();
      ctx.logger.info(`[getDropdownData] 用户有权限的企业数量: ${userEnterpriseIds.length}`);

      // 获取用户权限范围内的工种路径（支持多企业和层级配置）
      const workTypeQuery = {
        EnterpriseID: { $in: userEnterpriseIds },
        category: { $in: [ 'stations', 'workspaces', 'mills' ] }, // 支持岗位、工作区和工厂
        state: '1', // 启用状态
      };

      const workTypes = await ctx.service.db.find('FlatMillConstructionMaterialized', workTypeQuery);

      // 如果有多个企业，在工种路径前加上企业名称
      const workTypePaths = await this.formatWorkTypePathsWithEnterprise(workTypes, userEnterpriseIds);

      // 获取防护用品分类（使用与分类服务相同的逻辑）
      const categoryQuery = {
        $or: [
          { topEnterpriseId: { $in: userEnterpriseIds } },
          { isSystemDefault: true },
        ],
        isActive: true,
        // 移除 isLeaf 限制，获取所有分类用于验证
      };

      const categories = await ctx.service.db.find('ProtectionCategory', categoryQuery, null, {
        sort: { level: 1, sort: 1, createdAt: 1 },
      });

      // 对于模板下拉选项，只使用叶子节点
      const categoryPaths = categories
        .filter(cat => cat.isLeaf) // 只获取叶子节点用于下拉选择
        .map(cat => cat.path)
        .filter(path => path && path.trim() !== '') // 确保路径不为空
        .map(path => path.replace(/^\//, '')); // 去掉开头的斜线

      // 周期单位固定选项
      const cycleUnits = [ '天', '周', '月', '季', '年' ];

      return {
        workTypePaths,
        categoryPaths,
        cycleUnits,
      };

    } catch (error) {
      ctx.logger.error('获取下拉数据失败:', error);
      throw new Error(`获取下拉数据失败: ${error.message}`);
    }
  }

  /**
   * 获取用户有权限的企业ID列表
   * @returns {Array} 企业ID数组
   */
  async getUserEnterpriseIds() {
    const { ctx } = this;

    try {
      // 获取用户权限范围内的企业ID
      const enterpriseIds = await ctx.helper.getScopeData(
        'enterprise_ids',
        ctx.session.adminUserInfo.userid
      );

      if (enterpriseIds && enterpriseIds.length > 0) {
        ctx.logger.info(`[getUserEnterpriseIds] 从权限范围获取到企业ID: ${enterpriseIds.join(', ')}`);
        return enterpriseIds;
      }

      // 如果没有权限数据，使用当前用户的主企业ID
      const fallbackEnterpriseId = ctx.session.adminUserInfo.EnterpriseID;
      ctx.logger.info(`[getUserEnterpriseIds] 使用主企业ID作为备选: ${fallbackEnterpriseId}`);
      return [ fallbackEnterpriseId ];

    } catch (error) {
      ctx.logger.error('获取用户企业权限失败:', error);
      // 出错时使用当前用户的主企业ID
      const fallbackEnterpriseId = ctx.session.adminUserInfo.EnterpriseID;
      return [ fallbackEnterpriseId ];
    }
  }

  /**
   * 格式化工种路径，多企业时添加企业名称前缀
   * @param {Array} workTypes 工种数据
   * @param {Array} userEnterpriseIds 用户企业ID列表
   * @returns {Array} 格式化后的工种路径
   */
  async formatWorkTypePathsWithEnterprise(workTypes, userEnterpriseIds) {
    const { ctx } = this;

    try {
      // 检查实际有多少个不同的企业
      const uniqueEnterpriseIds = [ ...new Set(workTypes.map(wt => wt.EnterpriseID)) ];

      // 如果只有一个企业的工种数据，不需要添加企业名称前缀
      if (uniqueEnterpriseIds.length <= 1) {
        ctx.logger.info('[formatWorkTypePathsWithEnterprise] 只有一个企业，不添加企业名称前缀');
        return workTypes.map(wt => wt.fullPath).filter(path => path);
      }

      ctx.logger.info(`[formatWorkTypePathsWithEnterprise] 检测到 ${uniqueEnterpriseIds.length} 个企业，添加企业名称前缀`);

      // 获取企业信息
      const enterprises = await ctx.service.db.find('Adminorg', {
        _id: { $in: uniqueEnterpriseIds },
      }, 'cname shortName', { authCheck: false });

      ctx.logger.info('[formatWorkTypePathsWithEnterprise] 查询到企业信息:', enterprises.map(e => ({
        _id: e._id,
        cname: e.cname,
        shortName: e.shortName,
      })));

      // 创建企业ID到名称的映射
      const enterpriseMap = new Map();
      enterprises.forEach(enterprise => {
        const enterpriseName = enterprise.shortName || enterprise.cname || enterprise._id;
        enterpriseMap.set(enterprise._id, enterpriseName);
        ctx.logger.info(`[formatWorkTypePathsWithEnterprise] 映射企业: ${enterprise._id} -> ${enterpriseName}`);
      });

      // 为工种路径添加企业名称前缀
      const formattedPaths = workTypes.map(workType => {
        const enterpriseName = enterpriseMap.get(workType.EnterpriseID) || workType.EnterpriseID;
        return `【${enterpriseName}】${workType.fullPath}`;
      }).filter(path => path);

      ctx.logger.info(`[formatWorkTypePathsWithEnterprise] 格式化了 ${formattedPaths.length} 个工种路径`);
      return formattedPaths;

    } catch (error) {
      ctx.logger.error('格式化工种路径失败:', error);
      // 出错时返回原始路径
      return workTypes.map(wt => wt.fullPath).filter(path => path);
    }
  }

  /**
   * 生成示例数据
   * @param {Object} dropdownData 下拉数据
   * @returns {Array} 示例数据
   */
  generateSampleData(dropdownData) {
    const workTypePaths = dropdownData.workTypePaths.slice(0, 2); // 取前2个工种路径
    const categoryPaths = dropdownData.categoryPaths.slice(0, 4); // 取前4个分类路径

    if (workTypePaths.length === 0 || categoryPaths.length === 0) {
      // 如果没有数据，返回空示例
      return [];
    }

    const sampleData = [];

    // 第一个工种的具体配置示例
    if (workTypePaths[0]) {
      sampleData.push([ workTypePaths[0], categoryPaths[0] || '', 2, 1, '月', '具体工种配置' ]);
      if (categoryPaths[1]) sampleData.push([ '', categoryPaths[1], 1, 6, '月', '' ]);
      if (categoryPaths[2]) sampleData.push([ '', categoryPaths[2], 2, 3, '月', '' ]);
    }

    // 第二个工种的层级配置示例（如果有上级路径）
    if (workTypePaths[1]) {
      const workTypePath = workTypePaths[1];
      // 尝试获取上级路径
      const pathParts = workTypePath.split('/');
      const parentPath = pathParts.length > 1 ? pathParts.slice(0, -1).join('/') : workTypePath;

      sampleData.push([ parentPath, categoryPaths[3] || categoryPaths[0] || '', 1, 3, '月', '上级路径配置（自动应用到所有子级工种）' ]);
      if (categoryPaths[0]) sampleData.push([ '', categoryPaths[0], 2, 1, '月', '' ]);
    }

    return sampleData;
  }

  /**
   * 解析上传的Excel文件
   * @param {Stream} fileStream Excel文件流
   * @returns {Object} 解析结果
   */
  async parseExcelFile(fileStream) {
    const { ctx } = this;

    try {
      // 读取文件流数据
      const chunks = [];
      for await (const chunk of fileStream) {
        chunks.push(chunk);
      }
      const buffer = Buffer.concat(chunks);

      // 读取Excel文件，配置选项以更好地处理各种格式
      const workbook = XLSX.read(buffer, {
        type: 'buffer',
        cellDates: true,
        cellNF: false,
        cellText: false,
        raw: false,
      });

      const firstSheetName = workbook.SheetNames[0];

      if (!firstSheetName) {
        throw new Error('Excel文件中没有工作表');
      }

      const worksheet = workbook.Sheets[firstSheetName];

      ctx.logger.info(`[parseExcelFile] 工作表名称: ${firstSheetName}`);
      ctx.logger.info(`[parseExcelFile] 工作表范围: ${worksheet['!ref']}`);

      // 处理合并单元格
      this.fillMergedCells(worksheet);

      // 转换为JSON数据，使用更宽松的配置
      const rawData = XLSX.utils.sheet_to_json(worksheet, {
        defval: '',
        raw: false,
        dateNF: 'yyyy-mm-dd',
      });

      if (!rawData || rawData.length === 0) {
        throw new Error('Excel文件中没有数据');
      }

      // 解析数据
      const parsedData = this.parseStandardData(rawData);

      ctx.logger.info(`[parseExcelFile] 解析完成，有效数据: ${parsedData.length} 行`);

      return {
        success: true,
        data: parsedData,
        total: parsedData.length,
      };

    } catch (error) {
      ctx.logger.error('解析Excel文件失败:', error);
      throw new Error(`解析Excel文件失败: ${error.message}`);
    }
  }

  /**
   * 处理合并单元格
   * @param {Object} worksheet Excel工作表
   */
  fillMergedCells(worksheet) {
    if (!worksheet['!merges']) {
      return;
    }

    for (const mRange of worksheet['!merges']) {
      // 获取合并区域的起始单元格地址和值
      const startCell = XLSX.utils.encode_cell(mRange.s);
      const startValue = worksheet[startCell];

      // 如果起始单元格有值，则填充到合并范围内的所有单元格
      if (startValue && startValue.v !== undefined) {
        // 填充合并范围内的所有单元格
        for (let r = mRange.s.r; r <= mRange.e.r; r++) {
          for (let c = mRange.s.c; c <= mRange.e.c; c++) {
            const cellAddress = XLSX.utils.encode_cell({ c, r });
            // 只有当目标单元格为空或不存在时才填充
            if (!worksheet[cellAddress] || worksheet[cellAddress].v === undefined || worksheet[cellAddress].v === '') {
              worksheet[cellAddress] = {
                ...startValue,
                v: startValue.v,
                w: startValue.w || String(startValue.v),
                t: startValue.t || 's',
              };
            }
          }
        }
      }
    }

    // 清理合并信息
    delete worksheet['!merges'];
  }

  /**
   * 解析配发标准数据
   * @param {Array} rawData 原始数据
   * @returns {Array} 解析后的数据
   */
  parseStandardData(rawData) {
    const parsedData = [];
    const { ctx } = this;

    ctx.logger.info(`[parseStandardData] 开始解析数据，总行数: ${rawData.length}`);

    rawData.forEach((row, index) => {
      // 记录每行的原始数据用于调试
      ctx.logger.debug(`[parseStandardData] 第${index + 1}行原始数据:`, JSON.stringify(row));

      // 跳过完全空行，但不跳过只有部分字段为空的行
      const hasAnyData = Object.values(row).some(value =>
        value !== undefined && value !== null && String(value).trim() !== ''
      );

      if (!hasAnyData) {
        ctx.logger.debug(`[parseStandardData] 跳过空行: ${index + 1}`);
        return;
      }

      // 获取关键字段值
      const workTypePath = String(row['工种路径'] || '').trim();
      const categoryPath = String(row['防护用品分类'] || '').trim();

      // 如果关键字段都为空，跳过该行
      if (!workTypePath && !categoryPath) {
        ctx.logger.debug(`[parseStandardData] 跳过关键字段为空的行: ${index + 1}`);
        return;
      }

      // 解析工种路径，提取企业名称和实际路径
      const { enterpriseName, actualPath } = this.parseWorkTypePathWithEnterprise(workTypePath);

      const standardItem = {
        rowIndex: index + 1,
        workTypePath: actualPath, // 使用实际路径（不含企业名称）
        originalWorkTypePath: workTypePath, // 保留原始路径（含企业名称）
        enterpriseName, // 企业名称
        categoryPath,
        quantity: this.parseNumber(row['数量']),
        cycle: this.parseNumber(row['周期']),
        cycleUnit: String(row['单位'] || '').trim(),
        remark: String(row['备注'] || '').trim(),
      };

      ctx.logger.debug('[parseStandardData] 解析后的数据项:', JSON.stringify(standardItem));
      parsedData.push(standardItem);
    });
    ctx.logger.info(`[parseStandardData] 解析完成，有效数据行数: ${parsedData.length}`);
    return parsedData;
  }

  /**
   * 解析带企业名称前缀的工种路径
   * @param {String} workTypePath 工种路径（可能包含企业名称前缀）
   * @returns {Object} { enterpriseName, actualPath }
   */
  parseWorkTypePathWithEnterprise(workTypePath) {
    // 检查是否包含企业名称前缀格式：【企业名称】路径
    const enterpriseMatch = workTypePath.match(/^【(.+?)】(.+)$/);

    if (enterpriseMatch) {
      return {
        enterpriseName: enterpriseMatch[1].trim(),
        actualPath: enterpriseMatch[2].trim(),
      };
    }

    // 如果没有企业名称前缀，返回原路径
    return {
      enterpriseName: null,
      actualPath: workTypePath,
    };
  }

  /**
   * 解析数字
   * @param {*} value 值
   * @returns {Number} 数字
   */
  parseNumber(value) {
    // 处理空值或undefined
    if (value === null || value === undefined || value === '') {
      return 0;
    }

    // 转换为字符串并去除空格
    const strValue = String(value).trim();

    // 如果是空字符串，返回0
    if (strValue === '') {
      return 0;
    }

    // 尝试解析为数字
    const num = parseFloat(strValue);

    // 如果解析失败或不是有限数字，返回0
    if (isNaN(num) || !isFinite(num)) {
      return 0;
    }

    // 返回整数部分
    return Math.floor(Math.abs(num));
  }

  /**
   * 验证配发标准数据
   * @param {Array} data 解析后的数据
   * @returns {Object} 验证结果
   */
  async validateStandardData(data) {
    const { ctx } = this;
    const EnterpriseID = ctx.session.adminUserInfo.EnterpriseID;

    const validData = [];
    const invalidData = [];
    const validationErrors = [];

    try {
      // 获取验证所需的基础数据
      const validationContext = await this.getValidationContext(EnterpriseID);

      for (const item of data) {
        const errors = [];

        // 验证工种路径
        const workTypeValidation = this.validateWorkTypePath(
          item.workTypePath,
          item.enterpriseName,
          validationContext.workTypePaths,
          validationContext.allWorkTypes
        );
        if (!workTypeValidation.valid) {
          errors.push(`工种路径: ${workTypeValidation.error}`);
        }

        // 验证防护用品分类
        const categoryValidation = this.validateCategoryPath(item.categoryPath, validationContext.categories);
        if (!categoryValidation.valid) {
          errors.push(`防护用品分类: ${categoryValidation.error}`);
        }

        // 验证数量
        if (!item.quantity || item.quantity <= 0) {
          errors.push('数量必须为正整数');
        }

        // 验证周期
        if (!item.cycle || item.cycle <= 0) {
          errors.push('周期必须为正整数');
        }

        // 验证周期单位
        if (![ '天', '周', '月', '季', '年' ].includes(item.cycleUnit)) {
          errors.push('周期单位必须为 天/周/月/季/年 之一');
        }

        // 添加验证通过的额外信息
        if (errors.length === 0) {
          item.workTypeInfo = workTypeValidation.data;
          item.categoryInfo = categoryValidation.data;

          // 添加防护用品名称信息，方便前端显示
          item.protectionProductName = categoryValidation.data.name ||
                                      categoryValidation.data.label ||
                                      item.categoryPath.split('/').pop();

          validData.push(item);
        } else {
          item.errors = errors;
          invalidData.push(item);
          validationErrors.push({
            row: item.rowIndex,
            errors,
            // 即使验证失败，也尝试添加防护用品名称信息
            protectionProductName: categoryValidation.valid ?
              (categoryValidation.data.name || categoryValidation.data.label || item.categoryPath.split('/').pop()) :
              item.categoryPath.split('/').pop(),
          });
        }
      }

      return {
        success: true,
        validData,
        invalidData,
        validationErrors,
        statistics: {
          total: data.length,
          valid: validData.length,
          invalid: invalidData.length,
        },
      };

    } catch (error) {
      ctx.logger.error('验证配发标准数据失败:', error);
      throw new Error(`数据验证失败: ${error.message}`);
    }
  }

  /**
   * 获取验证上下文数据（支持多企业）
   * @returns {Object} 验证上下文
   */
  async getValidationContext() {
    const { ctx } = this;

    // 获取用户有权限的所有企业ID
    const userEnterpriseIds = await this.getUserEnterpriseIds();

    // 获取用户权限范围内的工种（支持多企业和层级配置）
    const workTypeQuery = {
      EnterpriseID: { $in: userEnterpriseIds },
      category: { $in: [ 'stations', 'workspaces', 'mills' ] }, // 支持岗位、工作区和工厂
      state: '1',
    };

    const workTypes = await ctx.service.db.find('FlatMillConstructionMaterialized', workTypeQuery);

    // 获取防护用品分类树（调用与前端相同的接口逻辑）
    let categoryTree;
    try {
      // 调用防护用品分类树服务，获取完整的分类信息
      categoryTree = await ctx.service.protectionCategory.getCategoryTree({
        includeSystem: true,
        activeOnly: true,
      });
    } catch (error) {
      ctx.logger.error('获取防护用品分类树失败:', error);
      // 降级到直接查询数据库
      const categories = await ctx.service.db.find('ProtectionCategory', {
        $or: [
          { topEnterpriseId: { $in: userEnterpriseIds } },
          { isSystemDefault: true },
        ],
        isActive: true,
      }, null, {
        sort: { level: 1, sort: 1, createdAt: 1 },
      });
      categoryTree = categories;
    }

    // 构建分类映射，支持分类树结构
    const categoryMap = {};

    // 递归处理分类树，提取所有分类信息
    const processCategoryTree = (categories, parentPath = '') => {
      if (!Array.isArray(categories)) return;

      for (const category of categories) {
        if (category.path) {
          const cleanPath = category.path.replace(/^\//, '');
          // 同时支持带斜线和不带斜线的路径
          categoryMap[cleanPath] = {
            ...category,
            // 确保有名称字段
            name: category.name || category.label || cleanPath.split('/').pop(),
          };
          categoryMap[category.path] = categoryMap[cleanPath];
        }

        // 递归处理子分类
        if (category.children && category.children.length > 0) {
          processCategoryTree(category.children, category.path);
        }
      }
    };

    // 处理分类树数据
    if (Array.isArray(categoryTree)) {
      processCategoryTree(categoryTree);
    } else if (categoryTree && Array.isArray(categoryTree.data)) {
      processCategoryTree(categoryTree.data);
    } else {
      // 如果分类树格式不符合预期，使用原有逻辑
      ctx.logger.warn('分类树格式不符合预期，使用降级逻辑');
      const fallbackCategories = Array.isArray(categoryTree) ? categoryTree : [];
      for (const cat of fallbackCategories) {
        if (cat.path) {
          const cleanPath = cat.path.replace(/^\//, '');
          categoryMap[cleanPath] = {
            ...cat,
            name: cat.name || cat.label || cleanPath.split('/').pop(),
          };
          categoryMap[cat.path] = categoryMap[cleanPath];
        }
      }
    }

    return {
      workTypePaths: workTypes.reduce((map, wt) => {
        if (wt.fullPath) {
          map[wt.fullPath] = wt;
        }
        return map;
      }, {}),
      allWorkTypes: workTypes, // 添加所有工种数据用于多企业验证
      categories: categoryMap,
    };
  }

  /**
   * 验证工种路径（支持层级配置和多企业）
   * @param {String} workTypePath 工种路径（实际路径，不含企业名称）
   * @param {String} enterpriseName 企业名称（可选）
   * @param {Object} workTypePaths 有效工种路径映射
   * @param {Array} allWorkTypes 所有工种数据
   * @returns {Object} 验证结果
   */
  validateWorkTypePath(workTypePath, enterpriseName, workTypePaths, allWorkTypes) {
    if (!workTypePath) {
      return { valid: false, error: '工种路径不能为空' };
    }

    // 如果指定了企业名称，需要验证企业和工种的匹配
    if (enterpriseName) {
      // 查找匹配的工种路径
      const matchingWorkTypes = allWorkTypes.filter(wt => {
        return wt.fullPath === workTypePath;
      });

      if (matchingWorkTypes.length === 0) {
        return { valid: false, error: `未找到工种路径"${workTypePath}"` };
      }

      // 暂时跳过企业名称的严格验证，因为验证时企业信息获取复杂
      // 在实际导入时会根据工种的 EnterpriseID 自动分配到正确的企业
      return { valid: true, data: matchingWorkTypes[0] };
    }

    // 原有的验证逻辑（兼容单企业）
    // 首先检查是否为完整的工种路径
    const exactMatch = workTypePaths[workTypePath];
    if (exactMatch) {
      return { valid: true, data: exactMatch };
    }

    // 检查是否为上级路径（层级配置）
    const hasChildPaths = Object.keys(workTypePaths).some(path =>
      path.startsWith(workTypePath + '/')
    );

    if (hasChildPaths) {
      // 创建一个虚拟的工种信息用于层级配置
      return {
        valid: true,
        data: {
          fullPath: workTypePath,
          name: workTypePath.split('/').pop(),
          isHierarchical: true, // 标记为层级配置
        },
      };
    }

    return { valid: false, error: '工种路径不存在或无权限访问' };
  }

  /**
   * 验证防护用品分类路径
   * @param {String} categoryPath 分类路径
   * @param {Object} categories 有效分类映射
   * @returns {Object} 验证结果
   */
  validateCategoryPath(categoryPath, categories) {
    if (!categoryPath) {
      return { valid: false, error: '防护用品分类不能为空' };
    }

    const categoryInfo = categories[categoryPath];
    if (!categoryInfo) {
      return { valid: false, error: '防护用品分类不存在' };
    }

    return { valid: true, data: categoryInfo };
  }

  /**
   * 批量导入配发标准（使用saveSingle方法，与编辑逻辑保持一致）
   * @param {Array} validData 验证通过的数据
   * @param {String} importStrategy 导入策略 (skip/overwrite/add)
   * @returns {Object} 导入结果
   */
  async batchImportStandardsUsingSaveSingle(validData, importStrategy = 'overwrite') {
    const { ctx } = this;

    const importResults = {
      success: 0,
      skipped: 0,
      updated: 0,
      created: 0,
      failed: 0,
      errors: [],
    };

    try {
      // 按工种分组数据
      const groupedData = this.groupDataByWorkType(validData);
      ctx.logger.info(`[batchImportStandardsUsingSaveSingle] 分组后的工种数量: ${Object.keys(groupedData).length}`);

      // 逐个工种处理
      for (const [ workTypeFullId, standards ] of Object.entries(groupedData)) {
        try {
          const result = await this.importWorkTypeStandardsUsingSaveSingle(
            workTypeFullId,
            standards,
            importStrategy
          );

          importResults.success += result.success;
          importResults.skipped += result.skipped;
          importResults.updated += result.updated;
          importResults.created += result.created;
          importResults.failed += result.failed;
          importResults.errors.push(...result.errors);

        } catch (error) {
          ctx.logger.error(`导入工种 ${workTypeFullId} 配发标准失败:`, error);
          importResults.failed += standards.length;
          importResults.errors.push({
            workType: workTypeFullId,
            error: error.message,
          });
        }
      }

      ctx.logger.info('[batchImportStandardsUsingSaveSingle] 导入完成:', importResults);
      return importResults;

    } catch (error) {
      ctx.logger.error('批量导入配发标准失败:', error);
      throw new Error(`批量导入失败: ${error.message}`);
    }
  }

  /**
   * 批量导入配发标准（原方法，保留兼容性）
   * @param {Array} validData 验证通过的数据
   * @param {String} importStrategy 导入策略 (skip/overwrite/add)
   * @returns {Object} 导入结果
   */
  async batchImportStandards(validData, importStrategy = 'overwrite') {
    const { ctx } = this;

    const importResults = {
      success: 0,
      skipped: 0,
      updated: 0,
      created: 0,
      failed: 0,
      errors: [],
    };

    try {
      // 扩展数据以支持层级配置（支持多企业）
      const expandedData = await this.expandHierarchicalDataMultiEnterprise(validData);
      console.log('扩展后的数据数量:', expandedData.length);
      console.log('扩展后的数据示例:', expandedData.slice(0, 3).map(item => ({
        workTypePath: item.workTypeInfo.fullPath,
        workTypeId: item.workTypeInfo.fullId,
        categoryPath: item.categoryPath,
      })));

      // 按工种分组数据
      const groupedData = this.groupDataByWorkType(expandedData);
      console.log('分组后的工种数量:', Object.keys(groupedData).length);
      console.log('分组的工种ID:', Object.keys(groupedData));

      for (const [ workTypeFullId, standards ] of Object.entries(groupedData)) {
        try {
          // 从工种信息中获取企业ID
          const enterpriseId = standards[0].workTypeInfo.EnterpriseID;
          const result = await this.importWorkTypeStandards(
            workTypeFullId,
            standards,
            importStrategy,
            enterpriseId
          );

          importResults.success += result.success;
          importResults.skipped += result.skipped;
          importResults.updated += result.updated;
          importResults.created += result.created;
          importResults.failed += result.failed;
          importResults.errors.push(...result.errors);

        } catch (error) {
          ctx.logger.error(`导入工种 ${workTypeFullId} 配发标准失败:`, error);
          importResults.failed += standards.length;
          importResults.errors.push({
            workType: workTypeFullId,
            error: error.message,
          });
        }
      }

      return {
        success: true,
        results: importResults,
        message: `导入完成：成功${importResults.success}条，跳过${importResults.skipped}条，失败${importResults.failed}条`,
      };

    } catch (error) {
      ctx.logger.error('批量导入配发标准失败:', error);
      throw new Error(`批量导入失败: ${error.message}`);
    }
  }

  /**
   * 扩展层级配置数据（支持多企业）
   * @param {Array} validData 验证通过的数据
   * @returns {Array} 扩展后的数据
   */
  async expandHierarchicalDataMultiEnterprise(validData) {
    const { ctx } = this;
    const expandedData = [];

    // 获取用户有权限的所有企业ID
    const userEnterpriseIds = await this.getUserEnterpriseIds();

    // 获取所有工种数据（支持多企业和层级配置）
    const allWorkTypes = await ctx.service.db.find('FlatMillConstructionMaterialized', {
      EnterpriseID: { $in: userEnterpriseIds },
      category: { $in: [ 'stations', 'workspaces', 'mills' ] }, // 支持岗位、工作区和工厂
      state: '1',
    });

    for (const item of validData) {
      const workTypePath = item.workTypeInfo.fullPath;

      // 查找所有以该路径开头的子级工种
      const matchingWorkTypes = allWorkTypes.filter(workType => {
        return workType.fullPath && workType.fullPath.startsWith(workTypePath);
      });

      if (matchingWorkTypes.length === 0) {
        // 如果没有匹配的工种，保留原数据
        expandedData.push(item);
      } else {
        // 为每个匹配的工种创建配置项
        for (const workType of matchingWorkTypes) {
          const expandedItem = {
            ...item,
            workTypeInfo: {
              fullId: workType.fullId, // 使用视图中的 fullId
              fullPath: workType.fullPath,
              name: workType.name || workType.fullPath.split('/').pop(),
              stationName: workType.stationName, // 添加岗位名称
              EnterpriseID: workType.EnterpriseID, // 添加企业ID
            },
          };
          expandedData.push(expandedItem);
        }
      }
    }

    return expandedData;
  }

  /**
   * 扩展层级配置数据（原方法，保持兼容性）
   * @param {Array} validData 验证通过的数据
   * @param {String} EnterpriseID 企业ID
   * @returns {Array} 扩展后的数据
   */
  async expandHierarchicalData(validData, EnterpriseID) {
    const { ctx } = this;
    const expandedData = [];

    // 获取所有工种数据（支持层级配置）
    const allWorkTypes = await ctx.service.db.find('FlatMillConstructionMaterialized', {
      EnterpriseID,
      category: { $in: [ 'stations', 'workspaces', 'mills' ] }, // 支持岗位、工作区和工厂
      state: '1',
    });

    for (const item of validData) {
      const workTypePath = item.workTypeInfo.fullPath;

      // 查找所有以该路径开头的子级工种
      const matchingWorkTypes = allWorkTypes.filter(workType => {
        return workType.fullPath && workType.fullPath.startsWith(workTypePath);
      });

      if (matchingWorkTypes.length === 0) {
        // 如果没有匹配的工种，保留原数据
        expandedData.push(item);
      } else {
        // 为每个匹配的工种创建配置项
        for (const workType of matchingWorkTypes) {
          const expandedItem = {
            ...item,
            workTypeInfo: {
              fullId: workType.fullId, // 修复：使用视图中的 fullId 而不是 _id
              fullPath: workType.fullPath,
              name: workType.name || workType.fullPath.split('/').pop(),
              stationName: workType.stationName, // 添加岗位名称
            },
          };
          expandedData.push(expandedItem);
        }
      }
    }

    return expandedData;
  }

  /**
   * 按工种分组数据
   * @param {Array} data 数据
   * @returns {Object} 分组后的数据
   */
  groupDataByWorkType(data) {
    const grouped = {};

    data.forEach(item => {
      const workTypeFullId = item.workTypeInfo.fullId;
      if (!grouped[workTypeFullId]) {
        grouped[workTypeFullId] = [];
      }
      grouped[workTypeFullId].push(item);
    });

    return grouped;
  }

  /**
   * 导入单个工种的配发标准（使用saveSingle方法）
   * @param {String} workTypeFullId 工种完整ID
   * @param {Array} standards 配发标准数组
   * @param {String} importStrategy 导入策略
   * @returns {Object} 导入结果
   */
  async importWorkTypeStandardsUsingSaveSingle(workTypeFullId, standards, importStrategy) {
    const { ctx } = this;

    const result = {
      success: 0,
      skipped: 0,
      updated: 0,
      created: 0,
      failed: 0,
      errors: [],
    };

    try {
      // 构建与手动编辑相同的数据结构
      const products = standards.map(standard => this.convertStandardToProduct(standard));

      // 构建saveSingle需要的数据结构
      const saveSingleData = {
        nodeFullId: workTypeFullId,
        products,
        category: '',
        configStatus: 'configured',
      };

      ctx.logger.info(`[importWorkTypeStandardsUsingSaveSingle] 准备导入工种: ${workTypeFullId}, 产品数量: ${products.length}`);
      ctx.logger.debug('[importWorkTypeStandardsUsingSaveSingle] 产品数据:', JSON.stringify(products, null, 2));

      // 调用saveSingle方法，保持与手动编辑一致的逻辑
      await ctx.service.defendproducts.saveSingle(saveSingleData);

      // 统计结果（简化处理，认为所有产品都成功导入）
      result.success = products.length;
      result.created = products.length; // 简化处理，不区分新建和更新

      ctx.logger.info(`[importWorkTypeStandardsUsingSaveSingle] 工种 ${workTypeFullId} 导入成功，产品数量: ${products.length}`);

      return result;

    } catch (error) {
      ctx.logger.error(`导入工种 ${workTypeFullId} 配发标准失败:`, error);
      result.failed = standards.length;
      result.errors.push({
        workType: workTypeFullId,
        error: error.message,
      });
      return result;
    }
  }

  /**
   * 导入单个工种的配发标准（原方法，保留兼容性）
   * @param {String} workTypeFullId 工种完整ID
   * @param {Array} standards 配发标准数组
   * @param {String} importStrategy 导入策略
   * @param {String} EnterpriseID 企业ID
   * @returns {Object} 导入结果
   */
  async importWorkTypeStandards(workTypeFullId, standards, importStrategy, EnterpriseID) {
    const { ctx } = this;

    const result = {
      success: 0,
      skipped: 0,
      updated: 0,
      created: 0,
      failed: 0,
      errors: [],
    };

    try {
      // 构建与手动配置相同的数据结构
      const products = standards.map(standard => this.createProductItemForSaveSingle(standard));

      // 构建saveSingle需要的数据结构
      const saveSingleData = {
        nodeFullId: workTypeFullId,
        products,
        category: '',
        configStatus: 'configured',
      };

      // 查找现有的配发标准以获取_id
      const existingPlan = await ctx.service.db.findOne('ProtectionPlan', {
        EnterpriseID,
        nodeFullId: workTypeFullId,
        planStatus: 1,
      });

      if (existingPlan) {
        saveSingleData._id = existingPlan._id;

        // 根据导入策略处理现有产品
        if (importStrategy === 'skip') {
          // 跳过策略：只添加不存在的产品
          const existingProducts = existingPlan.products || [];
          const newProducts = [];

          for (const newProduct of products) {
            const existingIndex = existingProducts.findIndex(existing =>
              existing.categoryId === newProduct.categoryId
            );

            if (existingIndex === -1) {
              newProducts.push(newProduct);
              result.created++;
            } else {
              result.skipped++;
            }
          }

          if (newProducts.length > 0) {
            saveSingleData.products = [ ...existingProducts, ...newProducts ];
          } else {
            // 没有新产品需要添加
            result.success = result.skipped;
            return result;
          }
        } else {
          // 覆盖策略：使用新的产品列表
          result.updated = products.length;
        }
      } else {
        // 新建配发标准
        result.created = products.length;
      }

      // 调用saveSingle方法，保持与手动配置一致的逻辑
      await ctx.service.defendproducts.saveSingle(saveSingleData);

      result.success = result.created + result.updated;
      return result;

    } catch (error) {
      ctx.logger.error(`导入工种 ${workTypeFullId} 配发标准失败:`, error);
      result.failed = standards.length;
      result.errors.push({
        workType: workTypeFullId,
        error: error.message,
      });
      return result;
    }
  }

  /**
   * 更新现有配发标准
   * @param {Object} existingPlan 现有配发标准
   * @param {Array} newStandards 新配发标准
   * @param {String} importStrategy 导入策略
   * @returns {Object} 更新结果
   */
  async updateExistingPlan(existingPlan, newStandards, importStrategy) {
    const { ctx } = this;

    const result = {
      success: 0,
      skipped: 0,
      updated: 0,
      created: 0,
      failed: 0,
      errors: [],
    };

    try {
      const existingProducts = existingPlan.products || [];
      const updatedProducts = [ ...existingProducts ];

      for (const standard of newStandards) {
        const categoryId = standard.categoryInfo._id;

        // 查找是否已存在相同分类的配发标准
        const existingIndex = existingProducts.findIndex(p => p.categoryId === categoryId);

        if (existingIndex !== -1) {
          const existing = existingProducts[existingIndex];

          // 检查是否完全相同（数量、周期、单位都相同）
          const isSame = existing.number === standard.quantity &&
                        existing.time === standard.cycle &&
                        existing.timeUnit === this.convertCycleUnitToEnglish(standard.cycleUnit);

          if (isSame && importStrategy === 'skip') {
            result.skipped++;
            continue;
          }

          if (importStrategy === 'overwrite' || !isSame) {
            // 覆盖或周期不同时更新
            updatedProducts[existingIndex] = this.createProductItem(standard);
            result.updated++;
          } else {
            result.skipped++;
          }
        } else {
          // 新增配发标准
          updatedProducts.push(this.createProductItem(standard));
          result.created++;
        }

        result.success++;
      }

      // 更新数据库
      const updateData = {
        products: updatedProducts,
        category: existingPlan.category || '',
        configStatus: 'configured',
      };

      await ctx.service.db.updateOne('ProtectionPlan',
        { _id: existingPlan._id },
        { $set: updateData }
      );

      // 更新配发标准后重新生成领用记录
      try {
        await ctx.service.receiveRecord.generateRecordsForPlan(existingPlan._id);
      } catch (error) {
        ctx.logger.error('更新配发标准后生成记录失败:', error);
      }

      return result;

    } catch (error) {
      ctx.logger.error('更新现有配发标准失败:', error);
      result.failed = newStandards.length;
      result.errors.push({
        planId: existingPlan._id,
        error: error.message,
      });
      return result;
    }
  }

  /**
   * 创建新的配发标准
   * @param {String} workTypeFullId 工种完整ID
   * @param {Array} standards 配发标准数组
   * @param {String} EnterpriseID 企业ID
   * @returns {Object} 创建结果
   */
  async createNewPlan(workTypeFullId, standards, EnterpriseID) {
    const { ctx } = this;

    const result = {
      success: standards.length,
      skipped: 0,
      updated: 0,
      created: standards.length,
      failed: 0,
      errors: [],
    };

    try {
      const workTypeInfo = standards[0].workTypeInfo;
      const products = standards.map(standard => this.createProductItem(standard));

      // 添加调试日志，查看 nodeFullId 的值
      ctx.logger.info(`[createNewPlan] 创建配发标准 - workTypeFullId: ${workTypeFullId}, workTypeInfo:`, {
        fullId: workTypeInfo.fullId,
        fullPath: workTypeInfo.fullPath,
        stationName: workTypeInfo.stationName,
        name: workTypeInfo.name,
      });

      const newPlan = {
        EnterpriseID,
        nodeFullId: workTypeFullId,
        nodeLevel: 'stations',
        nodeName: workTypeInfo.stationName || workTypeInfo.name,
        products,
        category: '',
        configStatus: 'configured',
      };

      const doc = await ctx.service.db.create('ProtectionPlan', newPlan);

      // 生成领用记录
      try {
        await ctx.service.receiveRecord.generateRecordsForPlan(doc._id);
      } catch (error) {
        ctx.logger.error('导入配发标准后生成记录失败:', error);
      }

      return result;

    } catch (error) {
      ctx.logger.error('创建新配发标准失败:', error);
      result.success = 0;
      result.created = 0;
      result.failed = standards.length;
      result.errors.push({
        workType: workTypeFullId,
        error: error.message,
      });
      return result;
    }
  }

  /**
   * 将导入的标准数据转换为与手动编辑相同的产品格式
   * @param {Object} standard 配发标准数据
   * @returns {Object} 产品配置项
   */
  convertStandardToProduct(standard) {
    return {
      _id: shortid.generate(),
      // 按照手动配置的逻辑设置字段（参考NewForm.vue中handleChangepProtection方法）
      product: standard.categoryInfo.name, // 设置为分类名称，与手动配置保持一致
      modelNumber: '',
      productIds: [],
      productType: [ standard.categoryInfo._id ], // 设置为分类ID数组，与级联选择器保持一致
      // 新分类系统字段
      categoryId: standard.categoryInfo._id,
      categoryPath: standard.categoryInfo.path ? standard.categoryInfo.path.replace(/^\//, '') : '', // 去掉开头的 /
      categoryName: standard.categoryInfo.name,
      // 配发标准字段
      number: standard.quantity,
      time: standard.cycle,
      timeUnit: this.convertCycleUnitToEnglish(standard.cycleUnit),
    };
  }

  /**
   * 创建产品配置项（用于saveSingle接口，与手动配置保持完全一致）
   * @param {Object} standard 配发标准
   * @returns {Object} 产品配置项
   */
  createProductItemForSaveSingle(standard) {
    return {
      _id: shortid.generate(),
      // 按照手动配置的逻辑设置字段（参考NewForm.vue中handleChangepProtection方法）
      product: standard.categoryInfo.name, // 设置为分类名称，与手动配置保持一致
      modelNumber: '',
      productIds: [],
      productType: [ standard.categoryInfo._id ], // 设置为分类ID数组，与级联选择器保持一致
      // 新分类系统字段
      categoryId: standard.categoryInfo._id,
      categoryPath: standard.categoryInfo.path ? standard.categoryInfo.path.replace(/^\//, '') : '', // 去掉开头的 /
      categoryName: standard.categoryInfo.name,
      // 配发标准字段
      number: standard.quantity,
      time: standard.cycle,
      timeUnit: this.convertCycleUnitToEnglish(standard.cycleUnit),
    };
  }

  /**
   * 创建产品配置项（保留原方法以兼容其他地方的调用）
   * @param {Object} standard 配发标准
   * @returns {Object} 产品配置项
   */
  createProductItem(standard) {
    return {
      _id: shortid.generate(),
      // 新分类系统字段
      categoryId: standard.categoryInfo._id,
      categoryPath: standard.categoryInfo.path ? standard.categoryInfo.path.replace(/^\//, '') : '', // 去掉开头的 /
      categoryName: standard.categoryInfo.name,
      // 按照手动配置的逻辑设置字段（参考NewForm.vue中handleChangepProtection方法）
      product: standard.categoryInfo.name, // 设置为分类名称，与手动配置保持一致
      modelNumber: '',
      productIds: [],
      productType: [ standard.categoryInfo._id ], // 设置为分类ID数组，与级联选择器保持一致
      // 配发标准字段
      number: standard.quantity,
      time: standard.cycle,
      timeUnit: this.convertCycleUnitToEnglish(standard.cycleUnit),
    };
  }

  /**
   * 将中文周期单位转换为英文
   * @param {String} chineseUnit 中文单位
   * @returns {String} 英文单位
   */
  convertCycleUnitToEnglish(chineseUnit) {
    const unitMap = {
      天: 'd',
      周: 'w',
      月: 'M',
      季: 'Q',
      年: 'y',
    };
    return unitMap[chineseUnit] || chineseUnit;
  }
}

module.exports = ProtectionStandardImportService;
