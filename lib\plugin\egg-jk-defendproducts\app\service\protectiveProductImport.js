/* eslint-disable no-case-declarations */
/* eslint-disable no-unused-vars */
/*
 * @Author: 系统重构
 * @Date: 2024-01-01
 * @Description: 防护用品产品导入服务
 * 支持基于分类模板的自定义字段导入
 */


const Service = require('egg').Service;
const XLSX = require('xlsx');
const ExcelJS = require('exceljs');

class ProtectiveProductImportService extends Service {

  /**
   * 根据分类生成导入模板
   * @param {String} categoryId 分类ID
   * @param {String} warehouseId 仓库ID
   * @returns {Object} Excel模板数据
   */
  async generateImportTemplate(categoryId, warehouseId) {
    const { ctx } = this;

    // 如果没有提供仓库ID，获取公共仓库ID
    if (!warehouseId) {
      const publicWarehouse = await ctx.model.Warehouse.findOne({
        EnterpriseID: ctx.session.adminUserInfo.EnterpriseID,
        isPublic: true,
      });
      if (!publicWarehouse) {
        throw new Error('未找到公共仓库，请联系管理员初始化仓库');
      }
      warehouseId = publicWarehouse._id;
    }

    try {
      // 获取分类信息
      const category = await ctx.service.db.findOne('ProtectionCategory', { _id: categoryId });
      if (!category) {
        throw new Error('分类不存在');
      }

      // 获取分类的模板配置
      const template = await ctx.service.db.findOne('ProtectiveProductTemplate', {
        EnterpriseID: ctx.session.adminUserInfo.EnterpriseID,
        categoryId,
        isActive: true,
      });

      // 如果没有自定义模板，使用默认配置
      const fieldConfig = template ? template.generateFieldConfig() : this.getDefaultFieldConfig();

      // 生成Excel模板结构
      const templateData = {
        sheetName: `${category.name}导入模板`,
        headers: [],
        sampleData: [],
        fieldMapping: {},
        validationRules: {},
      };

      // 添加基础字段
      const baseFields = [
        { key: 'product', label: '产品名称*', required: true, type: 'text' },
        { key: 'materialCode', label: '物料编码', required: false, type: 'text' },
        { key: 'modelNumber', label: '型号', required: false, type: 'text' },
        { key: 'productSpec', label: '产品规格', required: false, type: 'text' },
        { key: 'surplus', label: '库存数量', required: false, type: 'number' },
        { key: 'vender', label: '厂家', required: false, type: 'text' },
        // 有效期相关字段
        { key: 'hasExpiry', label: '是否有有效期', required: false, type: 'select', options: [ '是', '否' ] },
        { key: 'expiryPeriod', label: '有效期长度', required: false, type: 'number' },
        { key: 'expiryUnit', label: '有效期单位', required: false, type: 'select', options: [ '天', '月', '年' ] },
        { key: 'needProductionDate', label: '是否需要生产日期', required: false, type: 'select', options: [ '是', '否' ] },
      ];

      // 添加显示字段
      if (template && template.displayFields) {
        template.displayFields.forEach(field => {
          if (field.isVisible && !baseFields.find(bf => bf.key === field.fieldKey)) {
            baseFields.push({
              key: field.fieldKey,
              label: field.fieldLabel + (field.isRequired ? '*' : ''),
              required: field.isRequired,
              type: field.fieldType,
              options: field.options,
            });
          }
        });
      }

      // 添加自定义字段
      if (template && template.customFields) {
        template.customFields.forEach(field => {
          templateData.headers.push({
            key: `custom_${field.fieldKey}`,
            label: `${field.fieldLabel}${field.isRequired ? '*' : ''}`,
            required: field.isRequired,
            type: field.fieldType,
            options: field.options,
            unit: field.unit,
            description: field.description,
          });
        });
      }

      // 构建表头和字段映射
      baseFields.forEach(field => {
        templateData.headers.push(field);
        templateData.fieldMapping[field.label] = field.key;

        if (field.required) {
          templateData.validationRules[field.key] = { required: true };
        }
      });

      // 生成示例数据
      templateData.sampleData.push(this.generateSampleRow(templateData.headers, category));

      return templateData;
    } catch (error) {
      ctx.logger.error('生成导入模板失败:', error);
      throw new Error(`生成导入模板失败: ${error.message}`);
    }
  }

  /**
   * 生成Excel文件
   * @param {Object} templateData 模板数据
   * @returns {Buffer} Excel文件Buffer
   */
  async generateExcelFile(templateData) {
    // 如果是通用模板，使用ExcelJS生成带下拉选择的文件
    if (templateData.templateType === 'universal' && templateData.categories) {
      return await this.generateExcelWithDropdown(templateData);
    }

    // 否则使用XLSX生成普通文件
    const workbook = XLSX.utils.book_new();

    // 创建主工作表数据
    const wsData = [];

    // 添加表头
    const headerRow = templateData.headers.map(h => h.label);
    wsData.push(headerRow);

    // 添加示例数据
    templateData.sampleData.forEach(row => {
      wsData.push(templateData.headers.map(h => row[h.key] || ''));
    });

    // 创建工作表
    const worksheet = XLSX.utils.aoa_to_sheet(wsData);

    // 设置列宽（自适应）
    const colWidths = templateData.headers.map(h => {
      // 计算列内容的最大宽度
      let maxWidth = h.label.length;

      // 检查示例数据中的最大宽度
      templateData.sampleData.forEach(row => {
        const cellValue = String(row[h.key] || '');
        maxWidth = Math.max(maxWidth, cellValue.length);
      });

      // 设置最小宽度12，最大宽度50
      return { wch: Math.min(Math.max(maxWidth + 2, 12), 50) };
    });
    worksheet['!cols'] = colWidths;

    // 添加工作表到工作簿
    const sheetName = templateData.sheetName || '防护用品导入模板';
    XLSX.utils.book_append_sheet(workbook, worksheet, sheetName);

    // 生成Excel文件
    return XLSX.write(workbook, { type: 'buffer', bookType: 'xlsx' });
  }

  /**
   * 使用ExcelJS生成带下拉选择的Excel文件
   * @param {Object} templateData 模板数据
   * @returns {Buffer} Excel文件Buffer
   */
  async generateExcelWithDropdown(templateData) {
    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('防护用品导入模板');

    // 构建分类选项列表（只包含叶子节点，严格排除一级分类）
    const categoryOptions = [];
    templateData.categories.forEach(category => {
      // 严格过滤：必须是叶子节点且不是一级分类
      const isLeafNode = category.isLeaf === true;
      const isNotFirstLevel = category.level && category.level > 1;
      const hasPath = category.path && category.path.includes('/'); // 路径包含斜线说明不是一级

      if (isLeafNode && (isNotFirstLevel || hasPath)) {
        // 使用完整路径，去掉开头的斜线
        const categoryPath = category.path ? category.path.replace(/^\//, '') : category.name;
        // 确保路径包含斜线（即不是一级分类）
        if (categoryPath.includes('/')) {
          categoryOptions.push(categoryPath);
        }
      }
    });

    // 如果没有找到合适的分类，则添加所有包含斜线的分类（确保不是一级）
    if (categoryOptions.length === 0) {
      templateData.categories.forEach(category => {
        if (category.path && category.path.includes('/')) {
          const categoryPath = category.path.replace(/^\//, '');
          if (categoryPath.includes('/')) {
            categoryOptions.push(categoryPath);
          }
        }
      });
    }

    // 去重并排序
    const uniqueOptions = [ ...new Set(categoryOptions) ].sort();

    // 设置表头
    const headerRow = worksheet.addRow(templateData.headers.map(h => h.label));

    // 设置表头样式
    headerRow.eachCell((cell, colNumber) => {
      const header = templateData.headers[colNumber - 1];

      // 根据是否必填设置不同背景色
      if (header.required) {
        // 必填项：红色背景
        cell.fill = {
          type: 'pattern',
          pattern: 'solid',
          fgColor: { argb: 'FFDC143C' }, // 深红色背景
        };
      } else {
        // 非必填项：蓝色背景
        cell.fill = {
          type: 'pattern',
          pattern: 'solid',
          fgColor: { argb: 'FF4F81BD' }, // 蓝色背景
        };
      }

      cell.font = {
        color: { argb: 'FFFFFFFF' }, // 白色字体
        bold: true,
      };
      cell.alignment = {
        horizontal: 'center',
        vertical: 'middle',
      };
    });

    // 添加示例数据
    templateData.sampleData.forEach(row => {
      const dataRow = templateData.headers.map(h => row[h.key] || '');
      worksheet.addRow(dataRow);
    });

    // 设置列宽（智能自适应）
    templateData.headers.forEach((header, index) => {
      let maxWidth = header.label.length;

      // 检查示例数据中的最大宽度
      templateData.sampleData.forEach(row => {
        const cellValue = String(row[header.key] || '');
        maxWidth = Math.max(maxWidth, cellValue.length);
      });

      // 根据字段类型设置不同的宽度策略
      let columnWidth;
      if (header.key === 'categoryName') {
        // 分类名称列：稍微宽一些
        columnWidth = Math.min(Math.max(maxWidth + 3, 15), 25);
      } else if (header.key === 'product') {
        // 产品名称列：更宽一些
        columnWidth = Math.min(Math.max(maxWidth + 3, 20), 40);
      } else if (header.key === 'characteristic' || header.key === 'industryEnvironment' || header.key === 'remark') {
        // 描述性字段：更宽
        columnWidth = Math.min(Math.max(maxWidth + 3, 25), 50);
      } else if (header.key === 'surplus') {
        // 数字字段：较窄
        columnWidth = Math.min(Math.max(maxWidth + 2, 8), 12);
      } else {
        // 其他字段：标准宽度
        columnWidth = Math.min(Math.max(maxWidth + 2, 12), 30);
      }

      worksheet.getColumn(index + 1).width = columnWidth;
    });

    // 找到分类名称列的索引
    const categoryColumnIndex = templateData.headers.findIndex(h => h.key === 'categoryName');

    if (categoryColumnIndex !== -1) {
      // 为分类列添加数据验证（下拉选择）
      const categoryColumn = worksheet.getColumn(categoryColumnIndex + 1);

      // 设置数据验证范围（从第2行开始，到第1000行）
      for (let row = 2; row <= 1000; row++) {
        const cell = worksheet.getCell(row, categoryColumnIndex + 1);
        cell.dataValidation = {
          type: 'list',
          allowBlank: true,
          formulae: [ `"${uniqueOptions.join(',')}"` ],
        };
      }
    }

    // 生成Excel文件Buffer
    const buffer = await workbook.xlsx.writeBuffer();
    return buffer;
  }

  /**
   * 添加分类选项工作表
   * @param {Object} workbook 工作簿
   * @param {Array} categories 分类列表
   */
  addCategoryOptionsSheet(workbook, categories) {
    // 构建分类选项列表
    const categoryOptions = [ '分类名称（请从下方选择）' ];

    // 添加所有分类路径
    categories.forEach(category => {
      if (category.path) {
        // 去掉开头的斜杠
        const cleanPath = category.path.replace(/^\//, '');
        categoryOptions.push(cleanPath);
      } else {
        categoryOptions.push(category.name);
      }
    });

    // 去重并排序
    const uniqueOptions = [ ...new Set(categoryOptions) ];

    // 创建分类选项工作表数据
    const optionsData = uniqueOptions.map(option => [ option ]);

    // 创建工作表
    const optionsWorksheet = XLSX.utils.aoa_to_sheet(optionsData);

    // 设置列宽
    optionsWorksheet['!cols'] = [{ wch: 40 }];

    // 添加到工作簿
    XLSX.utils.book_append_sheet(workbook, optionsWorksheet, '分类选项');
  }


  /**
   * 解析导入文件
   * @param {String} filePath 文件路径
   * @param {String} categoryId 分类ID
   * @returns {Object} 解析结果
   */
  async parseImportFile(filePath, categoryId) {
    const { ctx } = this;

    try {
      // 读取Excel文件
      const workbook = XLSX.readFile(filePath);
      const firstSheetName = workbook.SheetNames[0];
      const worksheet = workbook.Sheets[firstSheetName];

      // 转换为JSON数据
      const rawData = XLSX.utils.sheet_to_json(worksheet, { defval: '' });

      if (!rawData || rawData.length === 0) {
        throw new Error('Excel文件中没有数据');
      }

      // 获取分类模板配置
      const template = await ctx.service.db.findOne('ProtectiveProductTemplate', {
        EnterpriseID: ctx.session.adminUserInfo.EnterpriseID,
        categoryId,
        isActive: true,
      });

      // 解析和验证数据
      const result = {
        validData: [],
        invalidData: [],
        fieldMapping: {},
        statistics: {
          total: rawData.length,
          valid: 0,
          invalid: 0,
        },
      };

      // 自动检测字段映射
      const detectedMapping = this.detectFieldMapping(Object.keys(rawData[0]), template);
      result.fieldMapping = detectedMapping;

      // 处理每行数据
      for (let i = 0; i < rawData.length; i++) {
        const row = rawData[i];
        const rowIndex = i + 2; // Excel行号从2开始

        try {
          const processedRow = await this.processImportRow(row, detectedMapping, template, categoryId);
          processedRow.rowIndex = rowIndex;
          result.validData.push(processedRow);
          result.statistics.valid++;
        } catch (error) {
          result.invalidData.push({
            rowIndex,
            originalData: row,
            error: error.message,
          });
          result.statistics.invalid++;
        }
      }

      return result;
    } catch (error) {
      ctx.logger.error('解析导入文件失败:', error);
      throw new Error(`解析导入文件失败: ${error.message}`);
    }
  }

  /**
   * 处理单行导入数据
   * @param {Object} row 原始行数据
   * @param {Object} fieldMapping 字段映射
   * @param {Object} template 模板配置
   * @param {String} categoryId 分类ID
   * @returns {Object} 处理后的数据
   */
  async processImportRow(row, fieldMapping, template, categoryId) {
    const { ctx } = this;

    const processedData = {
      EnterpriseID: ctx.session.adminUserInfo.EnterpriseID,
      categoryId,
      customAttributes: [],
    };

    // 处理基础字段
    Object.keys(fieldMapping).forEach(excelColumn => {
      const fieldKey = fieldMapping[excelColumn];
      const value = row[excelColumn];

      if (fieldKey.startsWith('custom_')) {
        // 处理自定义字段
        const customFieldKey = fieldKey.replace('custom_', '');
        const customField = template && template.customFields ? template.customFields.find(f => f.fieldKey === customFieldKey) : null;

        if (customField && value !== '') {
          processedData.customAttributes.push({
            key: customFieldKey,
            value: this.convertFieldValue(value, customField.fieldType),
            label: customField.fieldLabel,
            dataType: customField.fieldType,
          });
        }
      } else {
        // 处理基础字段
        if (value !== '') {
          processedData[fieldKey] = this.convertFieldValue(value, this.getFieldType(fieldKey));
        }
      }
    });

    // 验证必填字段
    this.validateRequiredFields(processedData, template);

    return processedData;
  }

  /**
   * 自动检测字段映射
   * @param {Array} excelColumns Excel列名
   * @param {Object} template 模板配置
   * @returns {Object} 字段映射
   */
  detectFieldMapping(excelColumns, template) {
    const mapping = {};

    // 基础字段映射规则
    const baseFieldRules = {
      产品名称: 'product',
      物料编码: 'materialCode',
      型号: 'modelNumber',
      产品规格: 'productSpec',
      库存数量: 'surplus',
      厂家: 'vender',
      防护类型: 'protectionType',
      防护用途: 'function',
      // 有效期相关字段映射
      是否有有效期: 'hasExpiry',
      有效期长度: 'expiryPeriod',
      有效期单位: 'expiryUnit',
      是否需要生产日期: 'needProductionDate',
    };

    // 检测基础字段
    excelColumns.forEach(column => {
      const cleanColumn = column.replace(/\*/g, ''); // 移除必填标记
      if (baseFieldRules[cleanColumn]) {
        mapping[column] = baseFieldRules[cleanColumn];
      }
    });

    // 检测自定义字段
    if (template && template.customFields) {
      template.customFields.forEach(field => {
        const matchingColumn = excelColumns.find(col =>
          col.replace(/\*/g, '') === field.fieldLabel
        );
        if (matchingColumn) {
          mapping[matchingColumn] = `custom_${field.fieldKey}`;
        }
      });
    }

    return mapping;
  }

  /**
   * 验证必填字段
   * @param {Object} data 数据
   * @param {Object} template 模板配置
   */
  validateRequiredFields(data, template) {
    // 验证产品名称（始终必填）
    if (!data.product || data.product.trim() === '') {
      throw new Error('产品名称不能为空');
    }

    // 验证模板中的必填字段
    if (template) {
      // 验证显示字段
      if (template.displayFields) {
        template.displayFields.forEach(field => {
          if (field.isRequired && (!data[field.fieldKey] || data[field.fieldKey] === '')) {
            throw new Error(`${field.fieldLabel}不能为空`);
          }
        });
      }

      // 验证自定义字段
      if (template.customFields) {
        template.customFields.forEach(field => {
          if (field.isRequired) {
            const customAttr = data.customAttributes.find(attr => attr.key === field.fieldKey);
            if (!customAttr || customAttr.value === '') {
              throw new Error(`${field.fieldLabel}不能为空`);
            }
          }
        });
      }
    }
  }

  /**
   * 转换字段值类型
   * @param {*} value 原始值
   * @param {String} fieldType 字段类型
   * @returns {*} 转换后的值
   */
  convertFieldValue(value, fieldType) {
    if (value === '' || value === null || value === undefined) {
      return null;
    }

    switch (fieldType) {
      case 'number':
        const num = Number(value);
        if (isNaN(num)) {
          throw new Error(`"${value}"不是有效的数字`);
        }
        return num;
      case 'boolean':
        // 处理中文是/否转换为布尔值
        const strValue = String(value).trim();
        if (strValue === '是' || strValue === 'true' || strValue === '1') {
          return true;
        } else if (strValue === '否' || strValue === 'false' || strValue === '0') {
          return false;
        }
        return Boolean(value);
      case 'array':
        return Array.isArray(value) ? value : [ value ];
      default:
        const stringValue = String(value).trim();
        // 特殊处理有效期单位的中文转换
        if (stringValue === '天') return 'days';
        if (stringValue === '月') return 'months';
        if (stringValue === '年') return 'years';
        return stringValue;
    }
  }

  /**
   * 获取字段类型
   * @param {String} fieldKey 字段键
   * @returns {String} 字段类型
   */
  getFieldType(fieldKey) {
    const numberFields = [ 'surplus', 'expiryPeriod' ];
    const arrayFields = [ 'harmFactors' ];
    const booleanFields = [ 'hasExpiry', 'needProductionDate' ];

    if (numberFields.includes(fieldKey)) return 'number';
    if (arrayFields.includes(fieldKey)) return 'array';
    if (booleanFields.includes(fieldKey)) return 'boolean';
    return 'string';
  }

  /**
   * 生成示例行数据
   * @param {Array} headers 表头配置
   * @param {Object} category 分类信息
   * @returns {Object} 示例数据
   */
  generateSampleRow(headers, category) {
    const sampleData = {};

    headers.forEach(header => {
      switch (header.key) {
        case 'product':
          sampleData[header.key] = `示例${category.name}`;
          break;
        case 'materialCode':
          sampleData[header.key] = 'MAT001';
          break;
        case 'modelNumber':
          sampleData[header.key] = 'MODEL-001';
          break;
        case 'surplus':
          sampleData[header.key] = 100;
          break;
        case 'vender':
          sampleData[header.key] = '示例厂家';
          break;
        // 有效期相关字段示例
        case 'hasExpiry':
          sampleData[header.key] = '是';
          break;
        case 'expiryPeriod':
          sampleData[header.key] = 12;
          break;
        case 'expiryUnit':
          sampleData[header.key] = '月';
          break;
        case 'needProductionDate':
          sampleData[header.key] = '否';
          break;
        default:
          if (header.options && header.options.length > 0) {
            sampleData[header.key] = header.options[0];
          } else {
            sampleData[header.key] = `示例${header.label.replace(/\*/g, '')}`;
          }
      }
    });

    return sampleData;
  }

  /**
   * 获取默认字段配置
   * @returns {Object} 默认配置
   */
  getDefaultFieldConfig() {
    return {
      displayFields: {},
      customFields: {},
      tableHeader: [
        { prop: 'product', label: '产品名称', width: 150 },
        { prop: 'materialCode', label: '物料编码', width: 120 },
        { prop: 'modelNumber', label: '型号', width: 120 },
        { prop: 'surplus', label: '库存', width: 100 },
        { prop: 'hasExpiry', label: '有有效期', width: 80 },
        { prop: 'expiryPeriod', label: '有效期', width: 80 },
        { prop: 'expiryUnit', label: '单位', width: 60 },
      ],
    };
  }

  /**
   * 导入产品数据
   * @param {Array} data 产品数据数组
   * @param {String} categoryId 分类ID
   * @param {String} warehouseId 仓库ID
   * @param {Object} options 导入选项
   * @returns {Object} 导入结果
   */
  async importProducts(data, categoryId, warehouseId, options = {}) {
    const { ctx } = this;

    // 如果没有提供仓库ID，获取公共仓库ID
    if (!warehouseId) {
      const publicWarehouse = await ctx.model.Warehouse.findOne({
        EnterpriseID: ctx.session.adminUserInfo.EnterpriseID,
        isPublic: true,
      });
      if (!publicWarehouse) {
        throw new Error('未找到公共仓库，请联系管理员初始化仓库');
      }
      warehouseId = publicWarehouse._id;
    }

    const result = {
      success: 0,
      failed: 0,
      skipped: 0,
      errors: [],
    };

    const EnterpriseID = ctx.session.adminUserInfo && ctx.session.adminUserInfo.EnterpriseID;

    for (let i = 0; i < data.length; i++) {
      const productData = data[i];

      try {
        // 设置基础信息
        productData.EnterpriseID = EnterpriseID;
        productData.warehouseId = warehouseId;
        productData.categoryId = categoryId;

        // 检查是否已存在
        if (options.skipDuplicates) {
          const existing = await ctx.service.db.findOne('ProtectiveProduct', {
            EnterpriseID,
            warehouseId,
            product: productData.product,
            modelNumber: productData.modelNumber,
          });

          if (existing) {
            result.skipped++;
            continue;
          }
        }

        // 创建产品
        await ctx.service.db.create('ProtectiveProduct', productData);
        result.success++;

      } catch (error) {
        result.failed++;
        result.errors.push({
          row: i + 1,
          data: productData,
          error: error.message,
        });
      }
    }

    return result;
  }

  /**
   * 获取产品列表
   * @param {Object} params 查询参数
   * @returns {Object} 产品列表
   */
  async getProductList(params = {}) {
    const { ctx } = this;
    const EnterpriseID = ctx.session.adminUserInfo && ctx.session.adminUserInfo.EnterpriseID;

    // 构建聚合管道
    const pipeline = [];

    // 匹配条件
    const matchCondition = { EnterpriseID };

    if (params.warehouseId) {
      matchCondition.warehouseId = params.warehouseId;
    }
    // 处理分类筛选（支持父子分类）
    if (params.categoryId) {
      console.log('getProductList - 分类筛选参数:', params.categoryId);
      // 获取选中分类及其所有子分类的ID
      const allCategoryIds = await this.getAllChildCategoryIds(params.categoryId);
      console.log('getProductList - 所有匹配的分类ID:', allCategoryIds);
      if (allCategoryIds.length > 0) {
        if (allCategoryIds.length === 1) {
          matchCondition.categoryId = allCategoryIds[0];
          console.log('getProductList - 使用单个分类ID查询');
        } else {
          matchCondition.categoryId = { $in: allCategoryIds };
          console.log('getProductList - 使用$in查询多个分类');
        }
      } else {
        // 如果没有找到任何分类ID，使用原始参数
        matchCondition.categoryId = params.categoryId;
        console.log('getProductList - 使用原始分类ID查询');
      }
    }
    if (params.isActive !== undefined) {
      matchCondition.isActive = params.isActive;
    }
    if (params.product) {
      matchCondition.product = new RegExp(params.product, 'i');
    }

    console.log('getProductList - 最终查询条件:', JSON.stringify(matchCondition, null, 2));
    pipeline.push({ $match: matchCondition });

    // 关联分类信息
    pipeline.push({
      $lookup: {
        from: 'protectioncategories',
        localField: 'categoryId',
        foreignField: '_id',
        as: 'categoryInfo',
      },
    });

    // 添加分类名称字段
    pipeline.push({
      $addFields: {
        categoryName: { $arrayElemAt: [ '$categoryInfo.name', 0 ] },
        categoryPath: { $arrayElemAt: [ '$categoryInfo.path', 0 ] },
      },
    });

    // 移除临时字段
    pipeline.push({
      $project: {
        categoryInfo: 0,
      },
    });

    // 排序
    pipeline.push({
      $sort: { createdAt: -1 },
    });

    // 分页参数
    const page = parseInt(params.page) || 1;
    const limit = parseInt(params.limit) || 20;
    const skip = (page - 1) * limit;

    // 使用 $facet 同时获取数据和总数
    pipeline.push({
      $facet: {
        data: [
          { $skip: skip },
          { $limit: limit },
        ],
        count: [
          { $count: 'total' },
        ],
      },
    });

    // 执行聚合查询
    const result = await ctx.service.db.aggregate('ProtectiveProduct', pipeline);

    const products = result[0] && result[0].data ? result[0].data : [];
    const total = result[0] && result[0].count && result[0].count[0] && result[0].count[0].total ? result[0].count[0].total : 0;

    console.log('getProductList - 查询结果统计:', {
      total,
      currentPageCount: products.length,
      page,
      limit,
    });

    if (products.length > 0) {
      console.log('getProductList - 前3个产品示例:', products.slice(0, 3).map(p => ({
        product: p.product,
        categoryId: p.categoryId,
        categoryName: p.categoryName,
      })));
    }

    return {
      list: products,
      total,
      page,
      limit,
      pages: Math.ceil(total / limit),
    };
  }

  /**
   * 保存产品
   * @param {Object} productData 产品数据
   * @returns {Object} 保存结果
   */
  async saveProduct(productData) {
    const { ctx } = this;
    const EnterpriseID = ctx.session.adminUserInfo && ctx.session.adminUserInfo.EnterpriseID;

    productData.EnterpriseID = EnterpriseID;

    if (productData._id && productData._id.trim() !== '') {
      // 更新产品
      const result = await ctx.service.db.updateOne('ProtectiveProduct',
        { _id: productData._id },
        productData
      );
      return result;
    }
    // 创建新产品 - 删除空的_id字段
    if (productData._id === '' || productData._id === null || productData._id === undefined) {
      delete productData._id;
    }
    const result = await ctx.service.db.create('ProtectiveProduct', productData);
    return result;

  }

  /**
   * 删除产品
   * @param {Array} ids 产品ID数组
   * @returns {Object} 删除结果
   */
  async deleteProducts(ids) {
    const { ctx } = this;
    const EnterpriseID = ctx.session.adminUserInfo && ctx.session.adminUserInfo.EnterpriseID;

    const result = await ctx.service.db.remove('ProtectiveProduct', {
      _id: { $in: ids },
      EnterpriseID,
    });

    return result;
  }

  /**
   * 生成通用导入模板
   * @param {String} warehouseId 仓库ID
   * @returns {Object} 模板数据
   */
  async generateUniversalTemplate(warehouseId) {
    const { ctx } = this;

    // 获取所有分类
    const categories = await ctx.service.db.find('ProtectionCategory', { isActive: true });

    // 基础字段（包含分类名称）
    const baseHeaders = [
      { key: 'categoryName', label: '分类名称', required: true, type: 'string', description: '请从下拉选择中选择' },
      { key: 'product', label: '产品名称', required: true, type: 'string' },
      { key: 'materialCode', label: '物料编码', required: true, type: 'string' },
      { key: 'modelNumber', label: '型号', required: false, type: 'string' },
      { key: 'productSpec', label: '产品规格', required: false, type: 'string' },
      { key: 'surplus', label: '库存数量', required: true, type: 'number' },
      { key: 'vender', label: '厂家', required: true, type: 'string' },
      { key: 'protectionType', label: '防护类型', required: false, type: 'string' },
      { key: 'function', label: '防护用途', required: false, type: 'string' },
      { key: 'harmFactors', label: '危害因素', required: false, type: 'array', description: '多个值用逗号分隔' },
      { key: 'characteristic', label: '特点', required: false, type: 'string' },
      { key: 'industryEnvironment', label: '使用环境', required: false, type: 'string' },
      // 有效期相关字段
      { key: 'hasExpiry', label: '是否有有效期', required: false, type: 'string', description: '填写"是"或"否"' },
      { key: 'expiryPeriod', label: '有效期长度', required: false, type: 'number', description: '数字，配合有效期单位使用' },
      { key: 'expiryUnit', label: '有效期单位', required: false, type: 'string', description: '填写"天"、"月"或"年"' },
      { key: 'needProductionDate', label: '是否需要生产日期', required: false, type: 'string', description: '填写"是"或"否"' },
      { key: 'remark', label: '备注', required: false, type: 'string' },
    ];

    // 收集所有分类的自定义字段
    const allCustomFields = new Map();
    categories.forEach(category => {
      if (category.attributes) {
        category.attributes.forEach(attr => {
          const key = `${attr.key}_${category.name}`;
          allCustomFields.set(key, {
            key,
            label: `${attr.label}(${category.name})`,
            required: false,
            type: attr.type || 'string',
            options: attr.options || [],
            categoryName: category.name,
            originalKey: attr.key,
          });
        });
      }
    });

    const customHeaders = Array.from(allCustomFields.values());

    return {
      templateType: 'universal',
      sheetName: '防护用品通用导入模板', // 添加sheetName字段，避免文件名undefined
      warehouseId,
      headers: [ ...baseHeaders, ...customHeaders ],
      sampleData: this.generateUniversalSampleData(baseHeaders, customHeaders, categories),
      categories: categories.map(cat => ({ _id: cat._id, name: cat.name, path: cat.path })),
    };
  }

  /**
   * 生成通用模板示例数据
   * @param {Array} baseHeaders 基础字段
   * @param {Array} customHeaders 自定义字段
   * @param {Array} categories 分类列表
   * @returns {Array} 示例数据
   */
  generateUniversalSampleData(baseHeaders, customHeaders, categories) {
    const sampleData = [];

    // 为叶子节点分类生成示例数据（严格排除一级分类）
    let leafCategories = categories.filter(cat => {
      const isLeafNode = cat.isLeaf === true;
      const isNotFirstLevel = cat.level && cat.level > 1;
      const hasPath = cat.path && cat.path.includes('/');
      return isLeafNode && (isNotFirstLevel || hasPath);
    });

    // 如果没有找到合适的分类，则使用所有包含斜线的分类
    if (leafCategories.length === 0) {
      leafCategories = categories.filter(cat => cat.path && cat.path.includes('/'));
    }

    // 如果还是没有，则使用所有非一级分类
    if (leafCategories.length === 0) {
      leafCategories = categories.filter(cat => cat.level && cat.level > 1);
    }

    // 取前3个作为示例
    leafCategories = leafCategories.slice(0, 3);

    leafCategories.forEach((category, index) => {
      const row = {};

      // 基础字段示例（分类名称使用完整路径）
      row.categoryName = category.path ? category.path.replace(/^\//, '') : category.name; // 使用完整路径，去掉开头斜线
      row.product = `示例产品${index + 1}`;
      row.materialCode = `MAT${String(index + 1).padStart(3, '0')}`;
      row.modelNumber = `MODEL-${index + 1}`;
      row.productSpec = '标准规格';
      row.surplus = 100;
      row.vender = '示例厂家';
      row.protectionType = '个人防护';
      row.function = '安全防护';
      row.harmFactors = '示例危害因素1,示例危害因素2';
      row.characteristic = '轻便、耐用';
      row.industryEnvironment = '建筑、制造';
      // 有效期字段示例
      row.hasExpiry = index === 0 ? '是' : '否'; // 第一个示例有有效期，其他没有
      row.expiryPeriod = index === 0 ? 12 : '';
      row.expiryUnit = index === 0 ? '月' : '';
      row.needProductionDate = index === 0 ? '是' : '否';
      row.remark = '示例备注';

      // 自定义字段示例（只填写当前分类相关的）
      customHeaders.forEach(header => {
        if (header.categoryName === category.name) {
          row[header.key] = `示例${header.label}`;
        } else {
          row[header.key] = ''; // 其他分类的字段留空
        }
      });

      sampleData.push(row);
    });

    return sampleData;
  }

  /**
   * 智能解析导入文件
   * @param {String} filePath 文件路径
   * @param {String} warehouseId 仓库ID
   * @returns {Object} 解析结果
   */
  async parseImportFileIntelligent(filePath, warehouseId) {
    const { ctx } = this;

    try {
      // 读取Excel文件
      const workbook = XLSX.readFile(filePath);
      const sheetName = workbook.SheetNames[0];
      const worksheet = workbook.Sheets[sheetName];
      const jsonData = XLSX.utils.sheet_to_json(worksheet);

      if (!jsonData || jsonData.length === 0) {
        throw new Error('Excel文件为空或格式不正确');
      }

      // 获取所有分类用于智能匹配
      const categories = await ctx.service.db.find('ProtectionCategory', { isActive: true });
      const categoryMap = this.buildCategoryMap(categories);

      const validData = [];
      const invalidData = [];

      for (let i = 0; i < jsonData.length; i++) {
        const row = jsonData[i];
        const rowIndex = i + 2; // Excel行号（从2开始，因为第1行是标题）

        try {
          // 智能识别分类
          const categoryInfo = this.intelligentCategoryRecognition(row.分类名称 || row.categoryName, categoryMap);

          if (!categoryInfo) {
            invalidData.push({
              rowIndex,
              error: `无法识别分类："${row.分类名称 || row.categoryName}"`,
              originalData: row,
            });
            continue;
          }

          // 构建产品数据
          const productData = this.buildProductDataFromRow(row, categoryInfo);
          productData.warehouseId = warehouseId;
          productData.EnterpriseID = ctx.session.adminUserInfo && ctx.session.adminUserInfo.EnterpriseID;

          validData.push(productData);
        } catch (error) {
          invalidData.push({
            rowIndex,
            error: error.message,
            originalData: row,
          });
        }
      }

      return {
        validData,
        invalidData,
        statistics: {
          total: jsonData.length,
          valid: validData.length,
          invalid: invalidData.length,
        },
      };
    } catch (error) {
      throw new Error(`文件解析失败: ${error.message}`);
    }
  }

  /**
   * 构建分类映射表
   * @param {Array} categories 分类列表
   * @returns {Map} 分类映射表
   */
  buildCategoryMap(categories) {
    const categoryMap = new Map();

    categories.forEach(category => {
      // 按名称映射
      categoryMap.set(category.name, category);

      // 按路径映射
      if (category.path) {
        categoryMap.set(category.path, category);
        // 去掉开头的斜杠
        const pathWithoutSlash = category.path.replace(/^\//, '');
        categoryMap.set(pathWithoutSlash, category);

        // 按路径的最后一段映射（叶子节点名称）
        const leafName = category.path.split('/').pop();
        if (leafName && leafName !== category.name) {
          categoryMap.set(leafName, category);
        }
      }

      // 按ID映射
      categoryMap.set(category._id, category);
    });

    return categoryMap;
  }

  /**
   * 智能分类识别
   * @param {String} categoryName 分类名称
   * @param {Map} categoryMap 分类映射表
   * @returns {Object|null} 分类信息
   */
  intelligentCategoryRecognition(categoryName, categoryMap) {
    if (!categoryName) {
      return null;
    }

    // 直接匹配分类名称
    if (categoryMap.has(categoryName)) {
      return categoryMap.get(categoryName);
    }

    // 精确匹配分类名称（忽略大小写）
    for (const [ key, category ] of categoryMap) {
      if (key.toLowerCase() === categoryName.toLowerCase()) {
        return category;
      }
    }

    // 模糊匹配（包含关系）
    for (const [ key, category ] of categoryMap) {
      if (key.includes(categoryName) || categoryName.includes(key)) {
        return category;
      }
    }

    return null;
  }

  /**
   * 从行数据构建产品数据
   * @param {Object} row 行数据
   * @param {Object} categoryInfo 分类信息
   * @returns {Object} 产品数据
   */
  buildProductDataFromRow(row, categoryInfo) {
    const productData = {
      categoryId: categoryInfo._id,
      categoryName: categoryInfo.name,
      categoryPath: categoryInfo.path,
      product: row.产品名称 || row.product,
      materialCode: row.物料编码 || row.materialCode,
      modelNumber: row.型号 || row.modelNumber,
      productSpec: row.产品规格 || row.productSpec,
      surplus: parseInt(row.库存数量 || row.surplus) || 0,
      vender: row.厂家 || row.vender,
      protectionType: row.防护类型 || row.protectionType,
      function: row.防护用途 || row.function,
      characteristic: row.特点 || row.characteristic,
      industryEnvironment: row.使用环境 || row.industryEnvironment,
      remark: row.备注 || row.remark,
      isActive: true,
      customAttributes: [],
    };

    // 处理有效期相关字段
    const hasExpiry = row.是否有有效期 || row.hasExpiry;
    if (hasExpiry) {
      const hasExpiryValue = String(hasExpiry).trim();
      productData.hasExpiry = hasExpiryValue === '是' || hasExpiryValue === 'true' || hasExpiryValue === '1';
    }

    const expiryPeriod = row.有效期长度 || row.expiryPeriod;
    if (expiryPeriod) {
      productData.expiryPeriod = parseInt(expiryPeriod) || null;
    }

    const expiryUnit = row.有效期单位 || row.expiryUnit;
    if (expiryUnit) {
      const unitValue = String(expiryUnit).trim();
      // 转换中文单位为英文
      if (unitValue === '天') {
        productData.expiryUnit = 'days';
      } else if (unitValue === '月') {
        productData.expiryUnit = 'months';
      } else if (unitValue === '年') {
        productData.expiryUnit = 'years';
      } else {
        productData.expiryUnit = unitValue;
      }
    }

    const needProductionDate = row.是否需要生产日期 || row.needProductionDate;
    if (needProductionDate) {
      const needProdDateValue = String(needProductionDate).trim();
      productData.needProductionDate = needProdDateValue === '是' || needProdDateValue === 'true' || needProdDateValue === '1';
    }

    // 处理危害因素
    const harmFactors = row.危害因素 || row.harmFactors;
    if (harmFactors) {
      productData.harmFactors = typeof harmFactors === 'string'
        ? harmFactors.split(/[,，]/).map(f => f.trim()).filter(f => f)
        : harmFactors;
    }

    // 处理自定义属性
    if (categoryInfo.attributes) {
      categoryInfo.attributes.forEach(attr => {
        const value = row[attr.label] || row[attr.key] || row[`${attr.key}_${categoryInfo.name}`];
        if (value !== undefined && value !== '') {
          productData.customAttributes.push({
            key: attr.key,
            label: attr.label,
            value: String(value),
            dataType: attr.type || 'string',
          });
        }
      });
    }

    return productData;
  }

  /**
   * 智能导入产品
   * @param {Array} data 产品数据数组
   * @param {String} warehouseId 仓库ID
   * @param {Object} options 导入选项
   * @returns {Object} 导入结果
   */
  async importProductsIntelligent(data, warehouseId, options = {}) {
    const { ctx } = this;
    const EnterpriseID = ctx.session.adminUserInfo && ctx.session.adminUserInfo.EnterpriseID;

    const result = {
      success: 0,
      failed: 0,
      skipped: 0,
      errors: [],
    };

    for (let i = 0; i < data.length; i++) {
      const productData = { ...data[i] };
      productData.EnterpriseID = EnterpriseID;
      productData.warehouseId = warehouseId;

      try {
        // 检查是否存在重复产品（只在同一仓库内检查）
        if (options.skipDuplicates) {
          const existing = await ctx.service.db.findOne('ProtectiveProduct', {
            product: productData.product,
            categoryId: productData.categoryId,
            warehouseId: productData.warehouseId,
            EnterpriseID,
          });

          if (existing) {
            if (options.updateExisting) {
              // 更新现有产品
              await ctx.service.db.updateOne('ProtectiveProduct',
                { _id: existing._id },
                productData
              );
              result.success++;
            } else {
              // 跳过重复产品
              result.skipped++;
            }
            continue;
          }
        }

        // 创建新产品
        await ctx.service.db.create('ProtectiveProduct', productData);
        result.success++;
      } catch (error) {
        result.failed++;
        result.errors.push({
          row: i + 1,
          error: error.message,
        });
      }
    }

    return result;
  }

  /**
   * 获取指定分类及其所有子分类的ID
   * @param {String} categoryId 分类ID
   * @returns {Array} 分类ID数组
   */
  async getAllChildCategoryIds(categoryId) {
    const { ctx } = this;

    try {
      console.log('getAllChildCategoryIds - 查找分类ID:', categoryId);

      // 获取分类树
      const categoryTree = await ctx.service.protectionCategory.buildCategoryTree({
        includeSystem: true,
        activeOnly: true,
      });

      console.log('getAllChildCategoryIds - 分类树节点数量:', categoryTree.length);

      // 递归查找所有子分类ID
      const allIds = new Set([ categoryId ]); // 包含自己

      const findChildIds = (nodes, targetId) => {
        for (const node of nodes) {
          if (node._id === targetId) {
            console.log('getAllChildCategoryIds - 找到目标分类:', node.name);
            // 找到目标分类，收集所有子分类ID
            const collectIds = children => {
              if (children && children.length > 0) {
                console.log('getAllChildCategoryIds - 收集子分类，数量:', children.length);
                for (const child of children) {
                  allIds.add(child._id);
                  console.log('getAllChildCategoryIds - 添加子分类ID:', child._id, child.name);
                  collectIds(child.children);
                }
              }
            };
            collectIds(node.children);
            return true;
          }

          // 递归查找子节点
          if (node.children && node.children.length > 0) {
            if (findChildIds(node.children, targetId)) {
              return true;
            }
          }
        }
        return false;
      };

      const found = findChildIds(categoryTree, categoryId);
      const result = Array.from(allIds);
      console.log('getAllChildCategoryIds - 查找结果:', found ? '找到' : '未找到');
      console.log('getAllChildCategoryIds - 最终分类ID列表:', result);

      return result;
    } catch (error) {
      console.error('getAllChildCategoryIds - 获取子分类ID失败:', error);
      ctx.logger.error('获取子分类ID失败:', error);
      // 如果获取失败，只返回原分类ID
      return [ categoryId ];
    }
  }
}

module.exports = ProtectiveProductImportService;
