/* eslint-disable jsdoc/require-param */
/* eslint-disable no-unused-vars */
const moment = require('moment');
const _ = require('lodash');
// const path = require('path');
// const fs = require('fs');

const Service = require('egg').Service;

class ReceiveRecordService extends Service {
  async getList({ params = {}, pagination, isFormat = true }) {
    const { ctx, app } = this;
    const EnterpriseID = ctx.session.adminUserInfo ? ctx.session.adminUserInfo.EnterpriseID : '';

    const matchQuery = { receiveDate: { $exists: true } }; // 筛选参数
    const pagingAggre = []; // 分页聚合

    // 支持关键字搜索
    if (params.keyword) {
      // matchQuery.category = { $regex: params.keyword };
    }

    if (params.parameterId) {
      matchQuery._id = params.parameterId;
    }

    // 支持按员工查询 - 支持前端的employee参数
    if (params.employee) {
      matchQuery.employee = params.employee;
    }

    // 支持日期范围查询
    if (params.startTime && params.endTime) {
      matchQuery.receiveDate = {
        $gte: new Date(params.startTime),
        $lt: new Date(params.endTime),
      };
    }

    // 支持分页查询
    if (pagination) {
      // 如果前端直接传递了current和pageSize，使用它们
      const current = pagination.current || 1;
      const pageSize = pagination.pageSize || 10;

      pagingAggre.push(...[
        {
          $limit: pageSize * current,
        },
        {
          $skip: pageSize * (current - 1),
        },
      ]);
    }
    console.log(matchQuery, 'matchQuery');
    const pipeline = [
      {
        $match: {
          EnterpriseID,
          ...matchQuery,
        },
      },
      {
        $facet: {
          doc: [
            {
              $sort: {
                receiveDate: -1,
                receiveStartDate: -1,
              },
            },
            {
              $lookup: {
                from: 'employees',
                localField: 'employee',
                foreignField: '_id',
                as: 'employeeInfo',
              },
            },
            {
              $unwind: {
                path: '$employeeInfo',
                preserveNullAndEmptyArrays: true, // 保留没有匹配到员工信息的记录
              },
            },
            ...pagingAggre,
          ],
          pageInfo: [
            { $count: 'total' },
          ],
        },
      },
    ];
    const doc = await ctx.service.db.aggregate('ReceiveRecord', pipeline);

    const tableData = JSON.parse(JSON.stringify(doc[0].doc));

    if (isFormat) {
      for (let i = 0; i < tableData.length; i++) {
        const item = tableData[i];
        // 处理可能没有employeeInfo的情况
        item.employee = item.employeeInfo ? item.employeeInfo.name : item.employee;
        item.receiveEndDate = item.receiveEndDate ? moment(item.receiveEndDate).format('YYYY-MM-DD') : '';
        item.receiveStartDate = item.receiveStartDate ? moment(item.receiveStartDate).format('YYYY-MM-DD') : '';
        item.receiveDate = item.receiveDate ? moment(item.receiveDate).format('YYYY-MM-DD') : '';
        item.sign = item.sign ? await ctx.helper.concatenatePath({
          path: `${app.config.image_upload_http_path}/${EnterpriseID}/${item.sign}`,
        }) : '';
        if (item.receiveDate && item.isRejected === false) {
          item.status = '已领取';
        } else if (item.receiveDate && item.isRejected === true) {
          item.status = '已拒绝';
        } else {
          item.status = '未领取';
        }
      }
    }

    const total = doc[0].pageInfo[0] ? doc[0].pageInfo[0].total : 0;

    return {
      tableData,
      total,
    };
  }
  create(data) {
    const { ctx } = this;
    delete data._id;
    return ctx.service.db.create('ReceiveRecord', data);
  }
  update(query, updateData, params = {}) {
    const { ctx } = this;
    return ctx.service.db.updateOne('ReceiveRecord', query, updateData, params);
  }
  remove(query) {
    const { ctx } = this;
    return ctx.service.db.deleteOne('ReceiveRecord', query);
  }
  // 根据计划生成领用记录
  async createReceiveRecordByPlan({ plan, hasCompleteEmployee, products, grantType }) {
    // console.log('createReceiveRecordByPlan', plan);
    const res = [];
    // 判单是否employee，若有则直接生成
    // 如果没有employee，则就生成对应车间岗位下的所有人
    if (plan.employee) {
      // 判断是否已经有了领用记录
      if (!hasCompleteEmployee.some(e => e.employee === plan.employee)) {
        const doc = await this.create(plan);
        res.push(doc);
      }
    } else {
      let employees = [];
      if (grantType === 'mill') {
        employees = await this.findMillEmployee({
          workshop: plan.workshop,
          workspaces: plan.workspaces,
          workstation: plan.workstation,
          EnterpriseID: plan.EnterpriseID,
        });
      } else if (grantType === 'depart') {
        employees = await this.findDepartEmployee({
          departId: plan.departId,
        });
      }

      for (let i = 0; i < employees.length; i++) {
        const employee = employees[i];
        const newPlan = _.cloneDeep(plan);
        newPlan.employee = employee;

        // 判断是否已经有了领用记录
        if (hasCompleteEmployee.some(e => e.employee === newPlan.employee)) {
          continue;
        }

        for (let j = 0; j < products.length; j++) {
          const product = products[j];
          newPlan.products = [ product ];
          // console.log('aaaaa', newPlan);
          const doc = await this.create(newPlan);
          res.push(doc);
        }
      }
    }
    return res;
  }
  async findMillEmployee({ workshop, workspaces, workstation, EnterpriseID }) {
    const { ctx } = this;
    const matchQuery = [
      { $match: { EnterpriseID } },
    ];
    // 厂房里的所有人
    if (workshop) {
      matchQuery.push({
        $match: {
          _id: workshop,
        },
      });

      if (workspaces) {
        matchQuery.push(...[
          {
            $unwind: '$children',
          },
          {
            $match: { 'children._id': workspaces },
          },
        ]);
      }
      if (workstation) {
        matchQuery.push(...[
          {
            $unwind: '$children.children',
          },
          {
            $match: { 'children.children._id': workstation },
          },
        ]);
      }
    } else {
      matchQuery.push({
        $match: {
          _id: workspaces,
        },
      });

      if (workstation) {
        matchQuery.push(...[
          {
            $unwind: '$children',
          },
          {
            $match: { 'children._id': workstation },
          },
        ]);
      }
    }

    const res = await ctx.service.db.aggregate('MillConstruction', matchQuery);
    const resStr = JSON.stringify(res);
    const matchRes = resStr.match(/"employees":".*?"/gi);
    let employeeList = [];
    if (matchRes) {
      matchRes.forEach(e => {
        const eArr = e.replace(/"/gi, '').split(':');
        if (eArr && eArr[1]) {
          employeeList.push(eArr[1]);
        }
      });
    }
    employeeList = Array.from(new Set(employeeList));
    return employeeList;
  }

  async findDepartEmployee({ departId }) {
    const employees = [];
    await this.findEmployeeByDepart(departId, employees);
    return employees;
  }

  async findEmployeeByDepart(departId, employees) {
    const { ctx } = this;
    // const depart = await ctx.model.Dingtree.findOne({ _id: departId });
    const depart = await ctx.service.db.findOne('Dingtree', { _id: departId });

    if (depart && depart.staff) {
      employees.push(...depart.staff);
    }

    const chiildDepart = await ctx.service.db.find('Dingtree', { parentid: departId });
    if (chiildDepart && chiildDepart.length > 0) {
      for (let i = 0; i < chiildDepart.length; i++) {
        const chiildDepartItem = chiildDepart[i];
        await this.findEmployeeByDepart(chiildDepartItem._id, employees);
      }
    }
  }

  // 导出领用记录
  async exportReceiveRecords(params) {
    const { ctx } = this;
    const EnterpriseID = ctx.session.adminUserInfo ? ctx.session.adminUserInfo.EnterpriseID : '';

    const startDate = new Date(params.time * 1 + '-01-01');
    const endDate = new Date(params.time * 1 + 1 + '-01-01');

    console.log(params, '这是里面的exportReceiveRecords');
    // const companyInfo = await ctx.model.Adminorg.findOne({ _id: EnterpriseID });
    const companyInfo = await ctx.service.db.findOne('Adminorg', { _id: EnterpriseID });
    console.log(companyInfo, 'companyInfo');
    const queryArr = [
      {
        $match: {
          EnterpriseID,
          receiveDate: {
            $gte: startDate,
            $lt: endDate,
          },
          isRejected: false,
          scrap: { $ne: true }, // 排除已报废的记录
        },
      },
      {
        $lookup: {
          from: 'employees',
          localField: 'employee',
          foreignField: '_id',
          as: 'employeeInfo',
        },
      },
      {
        $unwind: '$employeeInfo',
      },
      {
        $unwind: '$products',
      },
      {
        $project: {
          'products.product': 1,
          'products.modelNumber': 1,
          'products.number': 1,
          'employeeInfo.name': 1,
          receiveDate: 1,
        },
      },
    ];
    const res = await ctx.service.db.aggregate('ReceiveRecord', queryArr);
    if (res.length === 0) {
      return '获取数据失败';
    }

    for (let i = 0; i < res.length; i++) {
      const temp = res[i];
      temp.receiveDate = moment(temp.receiveDate).format('YYYY-MM-DD HH:mm');
      temp.product = temp.products.product;
      temp.modelNumber = temp.products.modelNumber;
      temp.number = temp.products.number;
      temp.employee = temp.employeeInfo.name;
      temp.index = i + 1;
      delete temp.products;
      delete temp.employeeInfo;
    }

    const formData = {
      year: params.time,
      companyName: companyInfo.cname,
      records: res,
    };

    console.log(formData, 'formData');

    const templateWordName = '个人防护用品发放表';
    const word = await ctx.helper.fillWord(ctx, templateWordName, { ...formData });
    return word.path;
  }

  /**
   * 获取岗位员工领用状态
   * @param {Object} params - 查询参数
   * @param {number} params.year - 年份
   * @param {number} params.month - 月份
   * @param {string} params.stationFullId - 岗位完整ID
   */
  async getStationEmployeeReceiveStatus({ year, month, stationFullId }) {
    const { ctx } = this;
    const EnterpriseID = ctx.session.adminUserInfo ? ctx.session.adminUserInfo.EnterpriseID : '';

    try {
      console.log('查询参数:', { year, month, stationFullId, EnterpriseID });

      // 1. 从物化视图获取岗位信息
      const stationInfo = await ctx.service.db.findOne('FlatMillConstructionMaterialized', {
        fullId: stationFullId,
        level: 'stations',
        EnterpriseID,
        state: '1',
      });

      console.log('岗位信息查询结果:', stationInfo);

      if (!stationInfo) {
        // 尝试不带state条件查询
        const stationInfoWithoutState = await ctx.service.db.findOne('FlatMillConstructionMaterialized', {
          fullId: stationFullId,
          level: 'stations',
          EnterpriseID,
        });
        console.log('不带state条件的岗位信息:', stationInfoWithoutState);

        if (!stationInfoWithoutState) {
          throw new Error('岗位信息不存在，请检查岗位ID: ' + stationFullId);
        } else {
          throw new Error('岗位已禁用');
        }
      }

      // 2. 获取该岗位的配发标准
      const protectionPlan = await ctx.service.db.findOne('ProtectionPlan', {
        nodeFullId: stationFullId,
        EnterpriseID,
      });

      console.log('配发标准查询结果:', protectionPlan);

      // 3. 构建查询时间范围
      const startDate = new Date(year, month - 1, 1);
      const endDate = new Date(year, month, 0, 23, 59, 59);

      console.log('时间范围:', { startDate, endDate });
      console.log('岗位员工列表:', stationInfo.employees);

      // 4. 获取岗位下所有员工和领用记录（优化：直接从员工表开始聚合）
      let employeeRecords = [];

      if (stationInfo.employees && stationInfo.employees.length > 0) {
        const pipeline = [
          // 1. 匹配岗位下的员工
          {
            $match: {
              _id: { $in: stationInfo.employees },
              status: { $ne: 0 }, // 排除已删除员工
              EnterpriseID,
            },
          },
          // 2. 联查领用记录
          {
            $lookup: {
              from: 'receiverecords',
              let: { employeeId: '$_id' },
              pipeline: [
                {
                  $match: {
                    $expr: { $eq: [ '$employee', '$$employeeId' ] },
                    EnterpriseID,
                    ...(protectionPlan ? { planId: protectionPlan._id } : {}),
                  },
                },
              ],
              as: 'receiveRecords',
            },
          },
          // 3. 格式化输出
          {
            $project: {
              _id: '$_id',
              employeeInfo: '$$ROOT',
              receiveRecords: '$receiveRecords',
            },
          },
        ];

        console.log('优化后的聚合查询管道:', JSON.stringify(pipeline, null, 2));

        employeeRecords = await ctx.service.db.aggregate('Employee', pipeline);

        console.log('员工记录查询结果数量:', employeeRecords.length);
      }


      // 5. 员工记录已包含所有岗位员工（包括没有领用记录的）
      console.log('所有员工记录已获取，数量:', employeeRecords.length);

      // 如果岗位下没有员工，也要返回基本信息
      if ((stationInfo.employees || []).length === 0) {
        console.log('该岗位下没有员工');
      }

      // 6. 计算每个员工的领用状态
      const requiredProducts = protectionPlan ? protectionPlan.products || [] : [];
      const employees = employeeRecords.map(record => {
        const employee = record.employeeInfo;
        const records = record.receiveRecords;

        if (!employee) {
          return null; // 跳过无效员工
        }

        const status = this.determineReceiveStatus(employee, requiredProducts, records);

        return {
          employeeId: employee._id,
          employeeName: employee.name,
          department: employee.departName || '未分配',
          unitCode: employee.unitCode,
          receiveStatus: status.status,
          statusLabel: status.label,
          statusColor: status.color,
          requiredProducts,
          receivedProducts: this.getReceivedProducts(records),
          receiveRecords: records,
          lastReceiveDate: this.getLastReceiveDate(records),
        };
      }).filter(Boolean); // 过滤掉null值

      console.log('最终员工数据:', employees);

      const result = {
        stationInfo: {
          _id: stationInfo._id,
          name: stationInfo.stationName,
          fullPath: stationInfo.fullPath,
          workspaceName: stationInfo.workspaceName,
          millName: stationInfo.millName,
          totalEmployeeCount: stationInfo.totalEmployeeCount,
        },
        protectionPlan: protectionPlan ? {
          _id: protectionPlan._id,
          products: protectionPlan.products,
          configStatus: protectionPlan.configStatus,
        } : null,
        employees,
        summary: {
          total: employees.length,
          completed: employees.filter(e => e.receiveStatus === 'completed').length,
          partial: employees.filter(e => e.receiveStatus === 'partial').length,
          pending: employees.filter(e => e.receiveStatus === 'pending').length,
          rejected: employees.filter(e => e.receiveStatus === 'rejected').length,
          expiring: employees.filter(e => e.receiveStatus === 'expiring').length,
          not_received: employees.filter(e => e.receiveStatus === 'not_received').length,
          partial_expired: employees.filter(e => e.receiveStatus === 'partial_expired').length,
        },
      };

      console.log('最终返回结果:', result);

      return result;

    } catch (error) {
      ctx.logger.error('获取岗位员工领用状态失败:', error);
      throw error;
    }
  }

  /**
   * 获取岗位汇总领用状态（新版本，基于聚合管道实现分页）
   * @param {Object} params - 查询参数
   * @param {number} params.year - 年份
   * @param {number} params.month - 月份
   * @param {string} params.workUnitId - 工作单元ID（可选）
   * @param {string} params.configStatus - 配发标准状态筛选 (configured/unconfigured/no_need)
   * @param {string} params.keyword - 关键词搜索（企业名称、工作场所路径等）
   * @param {number} params.page - 页码
   * @param {number} params.pageSize - 每页数量
   */
  async getStationSummaryReceiveStatus({ year, month, workUnitId, configStatus, keyword, page = 1, pageSize = 20 }) {
    const { ctx } = this;
    // const EnterpriseID = ctx.session.adminUserInfo ? ctx.session.adminUserInfo.EnterpriseID : '';

    try {
      console.log('=== 获取岗位汇总领用状态（新版本） ===');
      console.log('参数:', { year, month, workUnitId, configStatus, keyword, page, pageSize });

      const skip = (page - 1) * pageSize;

      // 构建聚合管道查询条件（参考防护用品配发标准的实现）
      const query = {
        category: 'stations',
        state: '1',
      };

      // 添加工作单元筛选
      if (workUnitId) {
        console.log('收到workUnitId筛选参数:', workUnitId);

        // 检查选中节点的信息，判断是企业还是下级节点
        const selectedNode = await ctx.service.db.findOne(
          'FlatMillConstructionMaterialized',
          { _id: workUnitId }
        );
        console.log('选中节点信息:', selectedNode);

        if (selectedNode) {
          const escapedWorkUnitId = workUnitId.replace(
            /[.*+?^${}()|[\]\\]/g,
            '\\$&'
          );

          if (selectedNode.category === 'stations') {
            // 如果选中的是岗位级别，直接匹配该岗位
            query._id = workUnitId;
            console.log('岗位级筛选，直接匹配 _id:', workUnitId);
          } else {
            // 如果是 mill/workspaces 级别，正则匹配 fullId 后面加下划线的模式
            query.fullId = new RegExp(`${escapedWorkUnitId}_`);
            console.log('上级节点筛选，正则匹配 fullId_xxx:', query.fullId);
          }
        } else {
          // 兜底：按企业ID筛选
          console.log('未找到节点，按企业ID筛选');
          query.EnterpriseID = workUnitId;
        }
      }

      // 添加关键词搜索条件
      if (keyword && keyword.trim()) {
        query.$or = [
          { name: new RegExp(keyword.trim(), 'i') },
          { stationName: new RegExp(keyword.trim(), 'i') },
          { fullPath: new RegExp(keyword.trim(), 'i') },
          { enterpriseName: new RegExp(keyword.trim(), 'i') },
          { millName: new RegExp(keyword.trim(), 'i') },
          { workspaceName: new RegExp(keyword.trim(), 'i') },
        ];
      }

      console.log('最终查询条件:', query);

      // 构建聚合管道（参考防护用品配发标准的实现）
      const pipeline = [
        // 1. 匹配岗位
        {
          $match: query,
        },

        // 2. 联查配发标准
        {
          $lookup: {
            from: 'protectionplans',
            let: { stationFullId: '$fullId' },
            pipeline: [
              {
                $match: {
                  $expr: {
                    $and: [
                      { $eq: [ '$nodeFullId', '$$stationFullId' ] },
                    ],
                  },
                },
              },
            ],
            as: 'protectionPlan',
          },
        },

        // 2.5. 联查员工信息
        {
          $lookup: {
            from: 'employees',
            let: { stationId: '$_id' },
            pipeline: [
              {
                $match: {
                  $expr: {
                    $and: [
                      { $eq: [ '$stationId', '$$stationId' ] },
                      { $ne: [ '$status', 0 ] }, // 排除已删除的员工
                    ],
                  },
                },
              },
              {
                $project: {
                  _id: 1,
                  name: 1,
                  status: 1,
                },
              },
            ],
            as: 'employees',
          },
        },

        // 3. 添加计算字段
        {
          $addFields: {
            // 获取配发标准信息
            protectionPlanData: { $arrayElemAt: [ '$protectionPlan', 0 ] },
          },
        },

        // 5. 计算配置状态
        {
          $addFields: {
            protectionPlans: {
              $ifNull: [ '$protectionPlanData.products', []],
            },
            configStatus: {
              $cond: {
                if: { $eq: [ '$protectionPlanData.configStatus', 'no_need' ] },
                then: 'no_need',
                else: {
                  $cond: {
                    if: {
                      $and: [
                        { $ne: [ '$protectionPlanData', null ] },
                        { $gt: [{ $size: { $ifNull: [ '$protectionPlanData.products', []] } }, 0 ] },
                      ],
                    },
                    then: 'configured',
                    else: 'unconfigured',
                  },
                },
              },
            },
            hasProtectionPlan: { $gt: [{ $size: '$protectionPlan' }, 0 ] },
          },
        },
      ];

      // 应用配发标准状态筛选
      if (configStatus) {
        pipeline.push({
          $match: {
            configStatus,
          },
        });
      }

      // 添加排序
      pipeline.push({
        $sort: {
          enterpriseName: 1,
          millName: 1,
          workspaceName: 1,
          stationName: 1,
          name: 1,
        },
      });

      // 使用 $facet 同时获取总数和分页数据（参考防护用品配发标准的实现）
      pipeline.push({
        $facet: {
          // 获取总数
          totalCount: [
            { $count: 'total' },
          ],
          // 获取分页数据
          paginatedData: [
            { $skip: skip },
            { $limit: pageSize },
          ],
        },
      });

      console.log('聚合管道查询条件:', JSON.stringify(pipeline, null, 2));
      const result = await ctx.service.db.aggregate('FlatMillConstructionMaterialized', pipeline);

      const facetResult = result[0];
      const totalStationsCount = facetResult.totalCount.length > 0 ? facetResult.totalCount[0].total : 0;
      const stationsWithPlans = facetResult.paginatedData;

      console.log('分页信息:', { page, pageSize, skip, total: totalStationsCount });
      console.log('聚合管道查询结果数量:', stationsWithPlans.length);

      // 简化处理：直接处理分页后的岗位数据
      console.log('开始处理岗位汇总数据...');

      // 简化处理：不查询具体的领用记录，只返回基础岗位信息
      console.log('简化处理，不查询领用记录以提高性能');

      // 4. 简化处理：直接构建岗位汇总数据，不查询领用记录
      const stationSummaries = stationsWithPlans.map(station => {
        const employees = station.employees || [];
        const employeeCount = employees.length;

        return {
          _id: station._id,
          fullId: station.fullId,
          enterpriseName: station.enterpriseName || '未知企业',
          millName: station.millName || '',
          workspaceName: station.workspaceName || '',
          stationName: station.stationName || station.name || '未知岗位',
          fullPath: station.fullPath || '',
          configStatus: station.configStatus || 'unconfigured',
          protectionPlans: station.protectionPlans || [],

          // 员工统计信息（简化版本）
          totalEmployees: employeeCount,
          completedEmployees: 0, // 简化版本暂不计算
          partialEmployees: 0,
          pendingEmployees: employeeCount, // 假设所有员工都是待处理状态
          rejectedEmployees: 0,
          expiringEmployees: 0,
          completionRate: 0, // 简化版本设为0

          // 兼容旧格式
          stationId: station._id,
          totalEmployeeCount: employeeCount,
          hasProtectionPlan: station.hasProtectionPlan,
          summary: {
            total: employeeCount,
            completed: 0,
            partial: 0,
            pending: employeeCount,
            rejected: 0,
            expiring: 0,
            not_received: 0,
            partial_expired: 0,
          },
        };
      });

      // 3. 返回岗位汇总数据（包含分页信息）
      return {
        stationSummaries,
        pagination: {
          page,
          pageSize,
          total: totalStationsCount,
        },
        totalStations: totalStationsCount, // 使用总数而不是当前页数量
        overallSummary: {
          totalEmployees: stationSummaries.reduce((sum, s) => sum + s.totalEmployeeCount, 0),
          totalCompleted: stationSummaries.reduce((sum, s) => sum + s.summary.completed, 0),
          totalPartial: stationSummaries.reduce((sum, s) => sum + s.summary.partial, 0),
          totalPending: stationSummaries.reduce((sum, s) => sum + s.summary.pending, 0),
          totalRejected: stationSummaries.reduce((sum, s) => sum + s.summary.rejected, 0),
          totalExpiring: stationSummaries.reduce((sum, s) => sum + s.summary.expiring, 0),
          totalNotReceived: stationSummaries.reduce((sum, s) => sum + s.summary.not_received, 0),
          totalPartialExpired: stationSummaries.reduce((sum, s) => sum + s.summary.partial_expired, 0),
          averageCompletionRate: stationSummaries.length > 0
            ? Math.round(stationSummaries.reduce((sum, s) => sum + s.completionRate, 0) / stationSummaries.length)
            : 0,
        },
      };

    } catch (error) {
      ctx.logger.error('获取岗位汇总领用状态失败:', error);
      throw error;
    }
  }

  /**
   * 计算员工领用状态
   * @param {Array} records - 员工的领用记录
   * @param {number} year - 年份
   * @param {number} month - 月份
   * @returns {Object} 状态信息
   */
  calculateEmployeeStatus(records, year, month) {
    if (!records || records.length === 0) {
      return { status: 'not_received' };
    }

    // 获取员工信息和配发标准
    const firstRecord = records[0];
    const employee = firstRecord.employeeInfo;

    if (!employee) {
      return { status: 'not_received' };
    }

    // 需要获取该员工的配发标准
    // 从记录中提取配发标准信息
    const requiredProducts = this.extractRequiredProductsFromRecords(records);

    // 使用正确的状态计算方法
    const status = this.determineReceiveStatus(employee, requiredProducts, records);

    return { status: status.status };
  }

  /**
   * 从领用记录中提取配发标准信息
   */
  extractRequiredProductsFromRecords(records) {
    const categoryMap = new Map();

    records.forEach(record => {
      if (record.categoryId && record.products && record.products.length > 0) {
        const product = record.products[0];
        if (!categoryMap.has(record.categoryId)) {
          categoryMap.set(record.categoryId, {
            categoryId: record.categoryId,
            categoryPath: record.categoryPath,
            categoryName: record.categoryName,
            product: product.product,
            number: product.number,
            // 从planId获取时间信息，这里简化处理
            time: 24,
            timeUnit: 'M',
          });
        }
      }
    });

    return Array.from(categoryMap.values());
  }

  /**
   * 获取岗位详细员工领用状态
   * @param {Object} params - 查询参数
   * @param {string} params.stationId - 岗位ID
   * @param {number} params.year - 年份
   * @param {number} params.month - 月份
   */
  async getStationDetailReceiveStatus({ stationId, year, month }) {
    const { ctx } = this;
    const EnterpriseID = ctx.session.adminUserInfo ? ctx.session.adminUserInfo.EnterpriseID : '';

    try {
      // 1. 获取岗位信息
      const stationInfo = await ctx.service.db.findOne('FlatMillConstructionMaterialized', {
        _id: stationId,
        category: 'stations', // 使用category而不是level
        state: '1',
      });

      if (!stationInfo) {
        throw new Error('岗位信息不存在或已禁用: ' + stationId);
      }

      // 2. 获取配发标准
      const protectionPlan = await ctx.service.db.findOne('ProtectionPlan', {
        nodeFullId: stationInfo.fullId,
      });

      // 3. 获取员工和领用记录（优化：直接从员工表开始聚合）
      const employeeIds = stationInfo.employees || [];
      let employeeRecords = [];

      if (employeeIds.length > 0) {
        // 构建时间范围查询条件
        const startDate = new Date(year, month - 1, 1);
        const endDate = new Date(year, month, 0, 23, 59, 59);

        const pipeline = [
          // 1. 匹配岗位下的员工
          {
            $match: {
              _id: { $in: employeeIds },
              status: { $ne: 0 }, // 排除已删除员工
              EnterpriseID,
            },
          },
          // 2. 联查领用记录
          {
            $lookup: {
              from: 'receiverecords',
              let: { employeeId: '$_id' },
              pipeline: [
                {
                  $match: {
                    $expr: { $eq: [ '$employee', '$$employeeId' ] },
                    ...(protectionPlan ? { planId: protectionPlan._id } : {}),
                    $or: [
                      { receiveStartDate: { $gte: startDate, $lte: endDate } },
                      { receiveDate: { $gte: startDate, $lte: endDate } },
                      { warningDate: { $gte: startDate, $lte: endDate } },
                      { $and: [{ receiveDate: { $exists: false } }, { sign: { $exists: false } }] }, // 未领用的记录
                    ],
                  },
                },
              ],
              as: 'receiveRecords',
            },
          },
          // 3. 格式化输出
          {
            $project: {
              _id: '$_id',
              employeeInfo: '$$ROOT',
              receiveRecords: '$receiveRecords',
            },
          },
        ];

        employeeRecords = await ctx.service.db.aggregate('Employee', pipeline);
      }

      // 4. 计算每个员工的领用状态
      const requiredProducts = protectionPlan ? protectionPlan.products || [] : [];
      const processedEmployees = employeeRecords.map(record => {
        const employee = record.employeeInfo;
        const records = record.receiveRecords;

        if (!employee) {
          return null;
        }

        const status = this.determineReceiveStatus(employee, requiredProducts, records);

        return {
          employeeId: employee._id,
          employeeName: employee.name,
          department: employee.departName || '未分配',
          unitCode: employee.unitCode,
          receiveStatus: status.status,
          statusLabel: status.label,
          statusColor: status.color,
          requiredProducts, // 应配发产品
          receivedProducts: this.getReceivedProducts(records),
          receiveRecords: records,
          lastReceiveDate: this.getLastReceiveDate(records),
        };
      }).filter(Boolean);

      return {
        stationInfo: {
          _id: stationInfo._id,
          name: stationInfo.stationName,
          fullPath: stationInfo.fullPath,
          level: stationInfo.level,
          totalEmployeeCount: processedEmployees.length,
        },
        protectionPlan: protectionPlan ? {
          _id: protectionPlan._id,
          products: protectionPlan.products,
          configStatus: protectionPlan.configStatus,
        } : null,
        employees: processedEmployees,
        summary: {
          total: processedEmployees.length,
          completed: processedEmployees.filter(e => e.receiveStatus === 'completed').length,
          partial: processedEmployees.filter(e => e.receiveStatus === 'partial').length,
          pending: processedEmployees.filter(e => e.receiveStatus === 'pending').length,
          rejected: processedEmployees.filter(e => e.receiveStatus === 'rejected').length,
          expiring: processedEmployees.filter(e => e.receiveStatus === 'expiring').length,
          not_received: processedEmployees.filter(e => e.receiveStatus === 'not_received').length,
          partial_expired: processedEmployees.filter(e => e.receiveStatus === 'partial_expired').length,
        },
      };

    } catch (error) {
      ctx.logger.error('获取工作单元员工领用状态失败:', error);
      throw error;
    }
  }

  /**
   * 判断员工领用状态（基于分类ID匹配）
   */
  determineReceiveStatus(employee, requiredProducts, receiveRecords) {
    const now = new Date();

    // 如果没有配发标准，返回无需配发
    if (!requiredProducts || requiredProducts.length === 0) {
      return {
        status: 'no_requirement',
        label: '无需配发',
        color: 'info',
        detail: '该岗位暂无配发标准',
      };
    }

    // 优先级1: 检查拒绝领用（有一个拒绝就算拒绝）
    const rejectedRecords = receiveRecords.filter(record => record.isRejected === true);
    if (rejectedRecords.length > 0) {
      return {
        status: 'rejected',
        label: '拒绝领用',
        color: 'info',
        detail: '拒绝' + rejectedRecords.length + '项',
      };
    }

    // 按分类ID统计配发标准和领用情况
    const requiredCategoryMap = new Map(); // 配发标准分类
    const receivedCategoryMap = new Map(); // 已领用且有效的分类
    const pendingCategoryMap = new Map(); // 待领取分类
    const expiringCategoryMap = new Map(); // 即将到期分类
    const expiredCategoryMap = new Map(); // 已过期分类

    // 统计配发标准中的分类
    requiredProducts.forEach(required => {
      if (required.categoryId) {
        requiredCategoryMap.set(required.categoryId, {
          categoryId: required.categoryId,
          categoryName: required.categoryName || required.categoryPath,
          product: required.product,
          number: required.number || 0,
          time: required.time,
          timeUnit: required.timeUnit,
        });
      }
    });

    // 统计领用记录中的分类状态
    receiveRecords.forEach(record => {
      if (!record.categoryId) return;

      const categoryId = record.categoryId;

      // 检查是否已领用（有领用时间且未拒绝且未报废）
      // 注意：sign字段可能为空字符串，所以只检查receiveDate
      const isReceived = record.receiveDate && !record.isRejected && !record.scrap;

      if (isReceived) {
        // 检查有效期状态
        if (record.warningDate) {
          const warningTime = new Date(record.warningDate);
          const daysDiff = (warningTime.getTime() - now.getTime()) / (1000 * 3600 * 24);

          if (daysDiff <= 0) {
            // 已过期
            expiredCategoryMap.set(categoryId, {
              categoryId,
              categoryName: record.categoryName,
              receiveDate: record.receiveDate,
              expiredDays: Math.abs(Math.floor(daysDiff)),
            });
          } else if (daysDiff <= 7) {
            // 即将到期（7天内）
            expiringCategoryMap.set(categoryId, {
              categoryId,
              categoryName: record.categoryName,
              receiveDate: record.receiveDate,
              daysLeft: Math.ceil(daysDiff),
            });
          } else {
            // 有效期内，算作已领用
            receivedCategoryMap.set(categoryId, {
              categoryId,
              categoryName: record.categoryName,
              receiveDate: record.receiveDate,
            });
          }
        } else {
          // 没有预警时间，直接算作已领用
          receivedCategoryMap.set(categoryId, {
            categoryId,
            categoryName: record.categoryName,
            receiveDate: record.receiveDate,
          });
        }
      } else if (!record.receiveDate && !record.sign && !record.isRejected) {
        // 待领取记录
        pendingCategoryMap.set(categoryId, {
          categoryId,
          categoryName: record.categoryName,
        });
      }
    });

    // 优先级2: 检查即将到期
    if (expiringCategoryMap.size > 0) {
      const minDays = Math.min(...Array.from(expiringCategoryMap.values()).map(item => item.daysLeft));
      return {
        status: 'expiring',
        label: '即将到期',
        color: 'warning',
        detail: minDays + '天后到期',
      };
    }

    // 优先级3: 检查是否有过期的分类
    if (expiredCategoryMap.size > 0) {
      const totalRequiredCategories = requiredCategoryMap.size;
      const totalValidCategories = receivedCategoryMap.size;
      const totalExpiredCategories = expiredCategoryMap.size;

      if (totalValidCategories + totalExpiredCategories === totalRequiredCategories) {
        // 所有分类都已领用，但有些过期了
        return {
          status: 'partial_expired',
          label: '部分过期',
          color: 'warning',
          detail: `${totalExpiredCategories}个分类已过期，需重新领取`,
        };
      }
      // 部分领用且有过期
      return {
        status: 'partial',
        label: '未领用完整',
        color: 'warning',
        detail: `已领用${totalValidCategories}/${totalRequiredCategories}个分类，${totalExpiredCategories}个已过期`,
      };

    }

    // 优先级4: 检查完成度
    const totalRequiredCategories = requiredCategoryMap.size;
    const totalReceivedCategories = receivedCategoryMap.size;
    const totalPendingCategories = pendingCategoryMap.size;

    // 已领用完整：所有配发标准的分类都已领用且在有效期内
    if (totalReceivedCategories === totalRequiredCategories) {
      return {
        status: 'completed',
        label: '已领用完整',
        color: 'success',
        detail: '已完成' + totalRequiredCategories + '个分类',
      };
    }

    // 未领用完整：只领取了部分配发标准的分类
    if (totalReceivedCategories > 0) {
      return {
        status: 'partial',
        label: '未领用完整',
        color: 'warning',
        detail: '已领用' + totalReceivedCategories + '/' + totalRequiredCategories + '个分类',
      };
    }

    // 待领取：系统已按照配发标准生成所需分类领用记录，等待员工确认
    if (totalPendingCategories > 0) {
      return {
        status: 'pending',
        label: '待领取',
        color: 'warning',
        detail: '有' + totalPendingCategories + '个分类待领取',
      };
    }

    // 未领用：系统还没有生成领用记录
    return {
      status: 'not_received',
      label: '未领用',
      color: 'danger',
      detail: '系统还没有生成领用记录',
    };
  }

  /**
   * 获取已领用的防护用品
   */
  getReceivedProducts(receiveRecords) {
    const receivedMap = new Map();

    receiveRecords.forEach(record => {
      // 修复领用判断逻辑：有领用时间且未拒绝且未报废才算已领用
      const isReceived = record.receiveDate && !record.isRejected && !record.scrap;
      if (isReceived) {
        record.products.forEach(product => {
          const key = product.product + '_' + (product.modelNumber || '');
          const existing = receivedMap.get(key) || {
            product: product.product,
            modelNumber: product.modelNumber,
            number: 0,
          };
          existing.number += product.number || 0;
          receivedMap.set(key, existing);
        });
      }
    });

    return Array.from(receivedMap.values());
  }

  /**
   * 获取最后领用日期
   */
  getLastReceiveDate(receiveRecords) {
    const receiveDates = receiveRecords
      .filter(record => record.receiveDate && !record.isRejected && !record.scrap)
      .map(record => new Date(record.receiveDate))
      .sort((a, b) => b.getTime() - a.getTime());

    return receiveDates.length > 0 ? receiveDates[0] : null;
  }

  /**
   * 通用的防护用品记录生成和检查方法
   * @param {Object} params - 参数对象
   * @param {String} params.employeeId - 特定员工ID（可选）
   * @param {String} params.planId - 特定配发标准ID（可选）
   * @param {String} params.source - 调用来源：'plan_change', 'schedule', 'on_demand'
   * @param {Boolean} params.forceRegenerate - 是否强制重新生成（默认false）
   * @param {Number} params.batchSize - 批处理大小（默认100）
   * @returns {Object} 处理结果
   */
  async ensureReceiveRecords(params = {}) {
    const { ctx } = this;
    const {
      employeeId = null,
      planId = null,
      source = 'unknown',
      forceRegenerate = false,
      batchSize = 100,
    } = params;

    const EnterpriseID = ctx.session.adminUserInfo ? ctx.session.adminUserInfo.EnterpriseID : '';

    try {
      ctx.logger.info(`[ensureReceiveRecords] 开始处理，来源: ${source}, 员工: ${employeeId}, 计划: ${planId}`);

      // 1. 参数预处理和验证
      const scope = await this.preprocessParams({ employeeId, planId, EnterpriseID });
      ctx.logger.info(`[ensureReceiveRecords] 处理范围 - 员工数: ${scope.employeeIds.length}, 计划数: ${scope.planIds.length}`);

      // 2. 批量加载基础数据
      const dataContext = await this.loadDataContext(scope);
      ctx.logger.info(`[ensureReceiveRecords] 数据加载完成 - 计划: ${dataContext.plans.length}, 现有记录: ${dataContext.existingRecords.records.length}`);

      // 3. 计算需要的操作
      const operations = await this.calculateOperations(dataContext, { source, forceRegenerate });
      ctx.logger.info(`[ensureReceiveRecords] 操作计算完成 - 待创建: ${operations.recordsToCreate.length}, 待更新: ${operations.recordsToUpdate.length}, 待删除: ${operations.recordsToDelete.length}`);

      // 4. 执行批量数据库操作
      const result = await this.executeBatchOperations(operations, batchSize);

      ctx.logger.info(`[ensureReceiveRecords] 处理完成，创建: ${result.created}, 更新: ${result.updated}, 删除: ${result.deleted}`);

      return {
        success: true,
        source,
        ...result,
      };

    } catch (error) {
      ctx.logger.error(`[ensureReceiveRecords] 处理失败: ${error.message}`, error);
      throw error;
    }
  }

  /**
   * 预处理参数，确定处理范围
   */
  async preprocessParams({ employeeId, planId, EnterpriseID }) {
    const { ctx } = this;

    let employeeIds = [];
    let planIds = [];

    // 构建查询条件 - 定时任务时EnterpriseID为空，查询所有企业
    const employeeQuery = employeeId ? { _id: employeeId } : (EnterpriseID ? { EnterpriseID } : {});
    const planQuery = planId ? { _id: planId } : {
      ...(EnterpriseID ? { EnterpriseID } : {}),
      planStatus: 1,
      configStatus: { $ne: 'no_need' },
    };

    if (employeeId) {
      // 处理特定员工
      employeeIds = [ employeeId ];
    } else {
      // 获取员工列表（定时任务时获取所有企业的员工）
      const employees = await ctx.service.db.find('Employee', employeeQuery, { _id: 1 }, { authCheck: false });
      employeeIds = employees.map(emp => emp._id);
    }

    if (planId) {
      // 处理特定配发标准
      planIds = [ planId ];
    } else {
      // 获取配发标准列表（定时任务时获取所有企业的配发标准）
      const plans = await ctx.service.db.find('ProtectionPlan', planQuery, { _id: 1 }, { authCheck: false });
      planIds = plans.map(plan => plan._id);
    }

    return { employeeIds, planIds, EnterpriseID };
  }

  /**
   * 批量加载基础数据
   */
  async loadDataContext({ employeeIds, planIds, EnterpriseID }) {
    const { ctx } = this;

    // 并行加载所有需要的数据
    const [ plans, existingRecords, lastReceiveDates ] = await Promise.all([
      this.loadProtectionPlans(planIds, EnterpriseID),
      this.loadExistingRecords(employeeIds, planIds, EnterpriseID),
      this.loadLastReceiveDates(employeeIds, EnterpriseID),
    ]);

    return {
      plans,
      existingRecords,
      lastReceiveDates,
    };
  }

  /**
   * 加载员工信息及其岗位关系
   */
  async loadEmployeesWithPositions(employeeIds, EnterpriseID) {
    const { ctx } = this;

    // 构建员工查询条件
    const employeeQuery = {
      _id: { $in: employeeIds },
      ...(EnterpriseID ? { EnterpriseID } : {}),
    };

    // 构建岗位查询条件
    const positionQuery = {
      employees: { $in: employeeIds },
      ...(EnterpriseID ? { EnterpriseID } : {}),
    };

    // 获取员工基本信息
    const employees = await ctx.service.db.find('Employee', employeeQuery, {}, { authCheck: false });

    // 获取员工岗位关系
    const employeePositions = await ctx.service.db.aggregate('FlatMillConstructionMaterialized', [
      {
        $match: positionQuery,
      },
      {
        $project: {
          fullId: 1,
          employees: 1,
          millName: 1,
          workspaceName: 1,
          stationName: 1,
        },
      },
    ], { authCheck: false });

    // 构建员工到岗位的映射
    const employeePositionMap = new Map();
    employeePositions.forEach(position => {
      position.employees.forEach(empId => {
        if (!employeePositionMap.has(empId)) {
          employeePositionMap.set(empId, []);
        }
        employeePositionMap.get(empId).push(position);
      });
    });

    // 合并员工信息和岗位信息
    return employees.map(employee => ({
      ...employee,
      positions: employeePositionMap.get(employee._id) || [],
    }));
  }

  /**
   * 加载配发标准
   */
  async loadProtectionPlans(planIds, EnterpriseID) {
    const { ctx } = this;

    const planQuery = {
      _id: { $in: planIds },
      ...(EnterpriseID ? { EnterpriseID } : {}),
      planStatus: 1,
      configStatus: { $ne: 'no_need' },
    };

    return await ctx.service.db.find('ProtectionPlan', planQuery, {}, { authCheck: false });
  }

  /**
   * 加载现有的领用记录
   */
  async loadExistingRecords(employeeIds, planIds, EnterpriseID) {
    const { ctx } = this;

    const recordQuery = {
      employee: { $in: employeeIds },
      planId: { $in: planIds },
      ...(EnterpriseID ? { EnterpriseID } : {}),
    };

    const records = await ctx.service.db.find('ReceiveRecord', recordQuery, {}, { authCheck: false });

    // 构建快速查找映射
    const recordMap = new Map();
    records.forEach(record => {
      const product = record.products && record.products[0] ? record.products[0].product : '';
      const key = `${record.employee}_${record.planId}_${product}`;
      recordMap.set(key, record);
    });

    return { records, recordMap };
  }

  /**
   * 加载员工最后领用时间
   */
  async loadLastReceiveDates(employeeIds, EnterpriseID) {
    const { ctx } = this;

    const matchQuery = {
      employee: { $in: employeeIds },
      receiveDate: { $exists: true },
      ...(EnterpriseID ? { EnterpriseID } : {}),
    };

    const lastReceives = await ctx.service.db.aggregate('ReceiveRecord', [
      {
        $match: matchQuery,
      },
      {
        $group: {
          _id: {
            employee: '$employee',
            product: '$products.product',
          },
          lastReceiveDate: { $max: '$receiveDate' },
        },
      },
    ], { authCheck: false });

    // 构建员工产品最后领用时间映射
    const lastReceiveDateMap = new Map();
    lastReceives.forEach(item => {
      const key = `${item._id.employee}_${item._id.product}`;
      lastReceiveDateMap.set(key, item.lastReceiveDate);
    });

    return lastReceiveDateMap;
  }

  /**
   * 计算需要的操作（新增/更新/删除）
   */
  async calculateOperations(dataContext, options) {
    const { ctx } = this;
    const { plans, existingRecords, lastReceiveDates } = dataContext;
    const { source, forceRegenerate } = options;

    const recordsToCreate = [];
    const recordsToUpdate = [];
    const recordsToDelete = [];

    ctx.logger.info(`[calculateOperations] 开始计算操作 - 配发标准数: ${plans.length}`);

    const involvedEmployees = new Set(); // 记录涉及的员工

    // 遍历每个配发标准
    for (const plan of plans) {
      if (!plan.nodeFullId) {
        ctx.logger.debug(`[calculateOperations] 配发标准 ${plan._id} 没有nodeFullId，跳过`);
        continue;
      }

      // 根据nodeFullId获取岗位信息和员工列表
      const positionInfo = await this.getPositionEmployees(plan.nodeFullId);
      if (!positionInfo || !positionInfo.employees || positionInfo.employees.length === 0) {
        ctx.logger.debug(`[calculateOperations] 配发标准 ${plan._id} 对应岗位 ${plan.nodeFullId} 没有员工`);
        continue;
      }

      ctx.logger.info(`[calculateOperations] 配发标准 ${plan._id} 对应岗位 ${positionInfo.fullPath} 有 ${positionInfo.employees.length} 个员工`);

      // 获取这些员工的详细信息
      const employees = await this.getEmployeeDetails(positionInfo.employees);

      // 为每个员工的每个产品生成记录
      for (const employee of employees) {
        involvedEmployees.add(`${employee._id}(${employee.name || employee.employeeName || '未知'})`);

        for (const product of plan.products) {
          const recordKey = `${employee._id}_${plan._id}_${product.product}`;
          const existingRecord = existingRecords.recordMap.get(recordKey);

          if (!existingRecord) {
            // 记录不存在，创建新记录
            const newRecord = await this.createNewRecordData(employee, product, plan, positionInfo, lastReceiveDates, source);
            recordsToCreate.push(newRecord);

          } else if (forceRegenerate) {
            // 强制重新生成模式
            const isReceived = this.isRecordReceived(existingRecord);

            if (isReceived) {
              // 已领用的记录保持不变，记录日志
              ctx.logger.info(`[calculateOperations] 保留已领用记录: 员工=${employee.name || employee.employeeName}, 产品=${product.product}, 记录ID=${existingRecord._id}`);
            } else {
              // 未领用的记录删除重建
              ctx.logger.info(`[calculateOperations] 重建未领用记录: 员工=${employee.name || employee.employeeName}, 产品=${product.product}, 记录ID=${existingRecord._id}`);
              recordsToDelete.push(existingRecord._id);
              const newRecord = await this.createNewRecordData(employee, product, plan, positionInfo, lastReceiveDates, source);
              recordsToCreate.push(newRecord);
            }

          } else if (this.shouldUpdateRecord(existingRecord, product, lastReceiveDates)) {
            // 需要更新记录
            const updatedRecord = this.updateRecordData(existingRecord, product, lastReceiveDates);
            recordsToUpdate.push(updatedRecord);
          }
        }
      }
    }

    // 输出涉及的员工信息
    if (involvedEmployees.size > 0) {
      ctx.logger.info(`[calculateOperations] 📋 涉及员工 (${involvedEmployees.size}人): ${Array.from(involvedEmployees).join(', ')}`);
    } else {
      ctx.logger.info('[calculateOperations] ⚠️ 没有员工匹配任何配发标准');
    }

    ctx.logger.info(`[calculateOperations] 计算完成 - 创建: ${recordsToCreate.length}, 更新: ${recordsToUpdate.length}, 删除: ${recordsToDelete.length}`);

    return { recordsToCreate, recordsToUpdate, recordsToDelete };
  }

  /**
   * 根据nodeFullId获取岗位信息和员工列表
   */
  async getPositionEmployees(nodeFullId) {
    const { ctx } = this;

    // 添加调试日志，查看查询的 nodeFullId
    ctx.logger.info(`[getPositionEmployees] 查询岗位信息 - nodeFullId: ${nodeFullId}`);

    const positionInfo = await ctx.service.db.findOne('FlatMillConstructionMaterialized', {
      fullId: nodeFullId,
    }, {}, { authCheck: false });

    if (!positionInfo) {
      ctx.logger.warn(`[getPositionEmployees] ⚠️ 未找到岗位信息: ${nodeFullId}`);

      // 尝试查询是否存在类似的记录
      const similarRecords = await ctx.service.db.find('FlatMillConstructionMaterialized', {
        fullId: { $regex: nodeFullId.split('_').pop() },
      }, 'fullId fullPath', { authCheck: false, limit: 5 });

      if (similarRecords.length > 0) {
        ctx.logger.info('[getPositionEmployees] 找到类似记录:', similarRecords.map(r => ({ fullId: r.fullId, fullPath: r.fullPath })));
      }

      return null;
    }

    ctx.logger.info(`[getPositionEmployees] ✅ 找到岗位: ${positionInfo.fullPath}, 员工数: ${positionInfo.employees ? positionInfo.employees.length : 0}`);

    return positionInfo;
  }

  /**
   * 获取员工详细信息
   */
  async getEmployeeDetails(employeeIds) {
    const { ctx } = this;

    if (!employeeIds || employeeIds.length === 0) {
      return [];
    }

    const employees = await ctx.service.db.find('Employee', {
      _id: { $in: employeeIds },
    }, {}, { authCheck: false });

    ctx.logger.debug(`[getEmployeeDetails] 查询 ${employeeIds.length} 个员工，找到 ${employees.length} 个`);

    return employees;
  }

  /**
   * 获取员工适用的配发标准（已废弃，保留兼容性）
   */
  getApplicablePlans(employee, allPlans) {
    const { ctx } = this;
    const applicablePlans = [];

    for (const plan of allPlans) {
      if (this.isPlanApplicableToEmployee(employee, plan)) {
        applicablePlans.push(plan);
      }
    }

    return applicablePlans;
  }

  /**
   * 判断配发标准是否适用于员工
   */
  isPlanApplicableToEmployee(employee, plan) {
    const { ctx } = this;

    // 只支持新的nodeFullId格式配发标准
    if (plan.nodeFullId) {
      const isApplicable = employee.positions.some(position => {
        const match = position.fullId === plan.nodeFullId;
        if (match) {
          ctx.logger.info(`[isPlanApplicableToEmployee] ✅ 员工 ${employee._id}(${employee.name || '未知'}) 匹配配发标准 ${plan._id} - 岗位: ${position.fullId}`);
        }
        return match;
      });

      if (!isApplicable) {
        const employeePositions = employee.positions.map(p => p.fullId).join(', ');
        ctx.logger.debug(`[isPlanApplicableToEmployee] ❌ 员工 ${employee._id}(${employee.name || '未知'}) 不匹配配发标准 ${plan._id} - 员工岗位: [${employeePositions}], 计划岗位: ${plan.nodeFullId}`);
      }

      return isApplicable;
    }

    ctx.logger.debug(`[isPlanApplicableToEmployee] ⚠️ 配发标准 ${plan._id} 没有nodeFullId字段，跳过员工 ${employee._id}`);
    return false;
  }

  /**
   * 创建新记录数据
   */
  async createNewRecordData(employee, product, plan, positionInfo, lastReceiveDates, source) {
    const { ctx } = this;

    // 计算领用时间
    const lastReceiveKey = `${employee._id}_${product.product}`;
    const lastReceiveDate = lastReceiveDates.get(lastReceiveKey);
    const receiveStartDate = this.calculateNextReceiveTime(lastReceiveDate, product);
    const warningDate = this.calculateWarningDate(receiveStartDate, product);

    // 获取完整的工作场所信息
    let workshopId = '',
      workshopName = '';
    let workspacesId = '',
      workspacesName = '';
    let workstationId = '',
      workstationName = '';

    if (positionInfo && positionInfo.category === 'stations') {
      // 岗位信息
      workstationId = positionInfo._id;
      workstationName = positionInfo.stationName || positionInfo.name || '';

      // 查找上级车间信息
      if (positionInfo.parentId) {
        try {
          const workspaceInfo = await ctx.service.db.findOne('FlatMillConstructionMaterialized', {
            _id: positionInfo.parentId,
            EnterpriseID: plan.EnterpriseID,
          }, 'workspaceName name parentId _id', { authCheck: false });

          if (workspaceInfo) {
            workspacesId = workspaceInfo._id;
            workspacesName = workspaceInfo.workspaceName || workspaceInfo.name || '';

            // 查找上级厂房信息
            if (workspaceInfo.parentId) {
              const workshopInfo = await ctx.service.db.findOne('FlatMillConstructionMaterialized', {
                _id: workspaceInfo.parentId,
                EnterpriseID: plan.EnterpriseID,
              }, 'millName name _id', { authCheck: false });

              if (workshopInfo) {
                workshopId = workshopInfo._id;
                workshopName = workshopInfo.millName || workshopInfo.name || '';
              }
            }
          }
        } catch (error) {
          ctx.logger.warn('获取工作场所层级信息失败:', error.message);
        }
      }
    }

    return {
      EnterpriseID: plan.EnterpriseID,
      workshop: workshopId,
      workshopName,
      workspaces: workspacesId,
      workspacesName,
      workstation: workstationId,
      workstationName,
      departId: plan.departId || '',
      departName: plan.departName || '',
      employee: employee._id,
      // 添加防护用品分类字段
      categoryId: product.categoryId || '',
      categoryPath: product.categoryPath || '',
      categoryName: product.categoryName || '',
      products: [{
        productIds: product.productIds || [],
        productType: product.productType || [],
        product: product.product,
        modelNumber: product.modelNumber,
        productSpec: product.productSpec || '',
        number: product.number,
      }],
      planId: plan._id,
      acknowledge: false,
      isRejected: false,
      recordSource: 0, // 发放记录
      receiveStartDate,
      warningDate,
      createAt: new Date(),
    };
  }

  /**
   * 判断是否需要更新记录
   */
  shouldUpdateRecord(existingRecord, product, lastReceiveDates) {
    // 如果记录已经被领用，检查是否需要生成下次记录
    if (existingRecord.receiveDate && existingRecord.sign) {
      const nextReceiveTime = this.calculateNextReceiveTime(existingRecord.receiveDate, product);
      return new Date() >= nextReceiveTime;
    }

    // TODO: 未领用记录长期未处理的预警机制
    // 如果员工有待领取记录但一直未领用，需要生成预警通知

    // 如果记录未领用但预警时间需要更新
    if (!existingRecord.warningDate) {
      return true;
    }

    return false;
  }

  /**
   * 计算产品到期时间
   * @param {Date} productionDate - 生产日期
   * @param {Number} expiryPeriod - 有效期长度
   * @param {String} expiryUnit - 有效期单位
   * @returns {Date|null} 到期日期
   */
  calculateProductExpiryDate(productionDate, expiryPeriod, expiryUnit) {
    if (!productionDate || !expiryPeriod || !expiryUnit) {
      return null;
    }

    const date = new Date(productionDate);

    switch (expiryUnit) {
      case 'days':
        date.setDate(date.getDate() + expiryPeriod);
        break;
      case 'months':
        date.setMonth(date.getMonth() + expiryPeriod);
        break;
      case 'years':
        date.setFullYear(date.getFullYear() + expiryPeriod);
        break;
      default:
        return null;
    }

    return date;
  }

  /**
   * 确认领用并记录生产日期
   * @param {String} recordId - 领用记录ID
   * @param {Object} confirmData - 确认数据
   * @param {Date} confirmData.productionDate - 生产日期（可选）
   * @param {String} confirmData.sign - 签名（可选）
   * @returns {Object} 更新结果
   */
  async confirmReceiveWithProductionDate(recordId, confirmData = {}) {
    const { ctx } = this;
    const { productionDate, sign } = confirmData;
    const EnterpriseID = ctx.session.adminUserInfo ? ctx.session.adminUserInfo.EnterpriseID : '';

    try {
      // 获取领用记录
      const record = await ctx.service.db.findOne('ReceiveRecord', {
        _id: recordId,
        EnterpriseID,
        receiveDate: { $exists: false },
        sign: { $exists: false },
        isRejected: false,
      });

      if (!record) {
        throw new Error('领用记录不存在或已处理');
      }

      // 获取产品信息以检查是否需要生产日期
      let needProductionDate = false;
      let productExpiryInfo = null;

      if (record.products && record.products.length > 0) {
        const product = record.products[0];

        // 通过产品名称查询产品信息
        const productInfo = await ctx.service.db.findOne('ProtectiveProduct', {
          EnterpriseID,
          product: product.product,
          modelNumber: product.modelNumber,
        });

        if (productInfo) {
          needProductionDate = productInfo.needProductionDate || false;
          productExpiryInfo = {
            hasExpiry: productInfo.hasExpiry,
            expiryPeriod: productInfo.expiryPeriod,
            expiryUnit: productInfo.expiryUnit,
          };
        }
      }

      if (needProductionDate && !productionDate) {
        throw new Error('该产品需要记录生产日期');
      }

      // 验证生产日期
      if (productionDate) {
        const prodDate = new Date(productionDate);
        const now = new Date();

        if (prodDate > now) {
          throw new Error('生产日期不能晚于当前日期');
        }

        // 检查生产日期是否过于久远（这里可以根据产品有效期来判断）
        const oneYearAgo = new Date();
        oneYearAgo.setFullYear(oneYearAgo.getFullYear() - 1);
        if (prodDate < oneYearAgo) {
          ctx.logger.warn(`生产日期较早: ${productionDate}, 记录ID: ${recordId}`);
        }
      }

      // 计算到期日期
      let expiryDate = null;
      if (productionDate && productExpiryInfo && productExpiryInfo.hasExpiry) {
        expiryDate = this.calculateProductExpiryDate(
          productionDate,
          productExpiryInfo.expiryPeriod,
          productExpiryInfo.expiryUnit
        );
      }

      // 更新领用记录
      const updateData = {
        acknowledge: true,
        receiveDate: new Date(),
      };

      if (productionDate) {
        updateData.productionDate = new Date(productionDate);
      }

      if (expiryDate) {
        updateData.expiryDate = expiryDate;
      }

      if (sign) {
        updateData.sign = sign;
      }

      const result = await ctx.service.db.updateOne('ReceiveRecord',
        { _id: recordId, EnterpriseID },
        { $set: updateData }
      );

      ctx.auditLog(
        '确认领用防护用品',
        `记录ID: ${recordId}, 生产日期: ${productionDate || '无'}, 到期日期: ${expiryDate || '无'}`,
        'info'
      );

      return {
        success: true,
        message: '确认领用成功',
        data: {
          recordId,
          productionDate: updateData.productionDate,
          expiryDate: updateData.expiryDate,
          receiveDate: updateData.receiveDate,
        },
      };

    } catch (error) {
      ctx.logger.error(`确认领用失败: ${error.message}`, error);
      throw error;
    }
  }

  /**
   * 获取员工待确认的领用记录
   * @param {String} employeeId - 员工ID
   * @returns {Array} 待确认的领用记录列表
   */
  async getPendingReceiveRecords(employeeId) {
    const { ctx } = this;
    const EnterpriseID = ctx.session.adminUserInfo ? ctx.session.adminUserInfo.EnterpriseID : '';

    try {
      const pipeline = [
        // 1. 匹配条件：未确认领用的记录
        {
          $match: {
            EnterpriseID,
            employee: employeeId,
            receiveDate: { $exists: false },
            sign: { $exists: false },
            isRejected: false,
          },
        },
        // 2. 联查产品信息
        {
          $lookup: {
            from: 'protectiveproducts',
            let: {
              productName: { $arrayElemAt: [ '$products.product', 0 ] },
              modelNumber: { $arrayElemAt: [ '$products.modelNumber', 0 ] },
            },
            pipeline: [
              {
                $match: {
                  $expr: {
                    $and: [
                      { $eq: [ '$EnterpriseID', EnterpriseID ] },
                      { $eq: [ '$product', '$$productName' ] },
                      { $eq: [ '$modelNumber', '$$modelNumber' ] },
                    ],
                  },
                },
              },
            ],
            as: 'productInfo',
          },
        },
        // 3. 格式化输出
        {
          $project: {
            _id: 1,
            products: 1,
            receiveStartDate: 1,
            warningDate: 1,
            categoryName: 1,
            productInfo: { $arrayElemAt: [ '$productInfo', 0 ] },
            needProductionDate: {
              $ifNull: [{ $arrayElemAt: [ '$productInfo.needProductionDate', 0 ] }, false ],
            },
            hasExpiry: {
              $ifNull: [{ $arrayElemAt: [ '$productInfo.hasExpiry', 0 ] }, false ],
            },
            expiryPeriod: { $arrayElemAt: [ '$productInfo.expiryPeriod', 0 ] },
            expiryUnit: { $arrayElemAt: [ '$productInfo.expiryUnit', 0 ] },
          },
        },
        // 4. 按时间排序
        {
          $sort: { receiveStartDate: 1 },
        },
      ];

      const records = await ctx.service.db.aggregate('ReceiveRecord', pipeline);

      // 格式化返回数据
      return records.map(record => {
        const product = record.products && record.products[0] ? record.products[0] : {};
        return {
          recordId: record._id,
          product: product.product || '',
          modelNumber: product.modelNumber || '',
          number: product.number || 0,
          categoryName: record.categoryName || '',
          receiveStartDate: record.receiveStartDate,
          warningDate: record.warningDate,
          needProductionDate: record.needProductionDate,
          hasExpiry: record.hasExpiry,
          expiryPeriod: record.expiryPeriod,
          expiryUnit: record.expiryUnit,
          expiryUnitText: this.getExpiryUnitText(record.expiryUnit),
        };
      });

    } catch (error) {
      ctx.logger.error(`获取待确认记录失败: ${error.message}`, error);
      throw error;
    }
  }

  /**
   * 获取有效期单位文本
   */
  getExpiryUnitText(unit) {
    const unitMap = {
      days: '天',
      months: '月',
      years: '年',
    };
    return unitMap[unit] || unit;
  }

  /**
   * 更新记录数据
   */
  updateRecordData(existingRecord, product, lastReceiveDates) {
    const warningDate = this.calculateWarningDate(existingRecord.receiveStartDate, product);

    return {
      _id: existingRecord._id,
      warningDate,
      updateAt: new Date(),
    };
  }

  /**
   * 计算下次领用时间
   */
  calculateNextReceiveTime(lastReceiveDate, product) {
    const moment = require('moment');

    if (!lastReceiveDate) {
      return new Date(); // 首次领用，立即可领
    }

    const { time, timeUnit } = product;
    if (!time || !timeUnit) {
      return new Date(); // 如果没有配置周期，立即可领
    }

    // TODO: 特殊防护用品的过期判断机制
    // 某些防护用品（如安全帽）需要根据生产日期而非领用时间判断过期
    // 需要在防护用品清单中完善相关配置字段，支持多种过期判断方式

    // 根据时间单位计算下次领用时间
    let unit = timeUnit;
    switch (timeUnit) {
      case 'd': unit = 'days'; break;
      case 'w': unit = 'weeks'; break;
      case 'M': unit = 'months'; break;
      case 'Q': unit = 'quarters'; break;
      case 'y': unit = 'years'; break;
      default: unit = 'days';
    }

    return moment(lastReceiveDate).add(time, unit).toDate();
  }

  /**
   * 计算预警时间
   */
  calculateWarningDate(receiveTime, product) {
    const moment = require('moment');

    // 默认提前7天预警
    return moment(receiveTime).subtract(7, 'days').toDate();
  }

  /**
   * 执行批量数据库操作
   */
  async executeBatchOperations(operations, batchSize = 100) {
    const { ctx } = this;
    const { recordsToCreate, recordsToUpdate, recordsToDelete } = operations;

    let created = 0;
    let updated = 0;
    let deleted = 0;

    try {
      // 批量删除
      if (recordsToDelete.length > 0) {
        for (let i = 0; i < recordsToDelete.length; i += batchSize) {
          const batch = recordsToDelete.slice(i, i + batchSize);
          await ctx.service.db.deleteMany('ReceiveRecord', { _id: { $in: batch } });
          deleted += batch.length;
        }
      }

      // 批量创建
      if (recordsToCreate.length > 0) {
        for (let i = 0; i < recordsToCreate.length; i += batchSize) {
          const batch = recordsToCreate.slice(i, i + batchSize);
          await ctx.service.db.insertMany('ReceiveRecord', batch);
          created += batch.length;
        }
      }

      // 批量更新
      if (recordsToUpdate.length > 0) {
        for (let i = 0; i < recordsToUpdate.length; i += batchSize) {
          const batch = recordsToUpdate.slice(i, i + batchSize);

          // 使用bulkWrite进行批量更新
          const bulkOps = batch.map(record => ({
            updateOne: {
              filter: { _id: record._id },
              update: { $set: record },
            },
          }));

          await ctx.model.ReceiveRecord.bulkWrite(bulkOps);
          updated += batch.length;
        }
      }

      return { created, updated, deleted };

    } catch (error) {
      ctx.logger.error(`[executeBatchOperations] 批量操作失败: ${error.message}`, error);
      throw error;
    }
  }

  /**
   * 判断记录是否已经被领用
   * @param {Object} record - 领用记录
   * @returns {Boolean} 是否已领用
   */
  isRecordReceived(record) {
    if (!record) return false;

    // 判断记录是否已经被领用的条件：
    // 1. 有签名字段 (sign) - 表示员工已确认领用
    // 2. 有领用日期 (receiveDate) - 表示已经领用
    // 3. 有生产日期 (productionDate) - 表示已经填写了产品信息
    // 4. 有到期日期 (expiryDate) - 表示已经填写了产品信息
    const isReceived = !!(record.sign || record.receiveDate || record.productionDate || record.expiryDate);

    // 添加调试日志
    if (isReceived) {
      console.log(`记录已领用: ID=${record._id}, sign=${!!record.sign}, receiveDate=${!!record.receiveDate}, productionDate=${!!record.productionDate}, expiryDate=${!!record.expiryDate}`);
    }

    return isReceived;
  }

  /**
   * 为特定员工生成缺失的记录（按需生成的简化版本）
   */
  async generateMissingRecordsForEmployee(employeeId) {
    return await this.ensureReceiveRecords({
      employeeId,
      source: 'on_demand',
    });
  }

  /**
   * 为特定配发标准生成记录（配发标准变更时使用）
   */
  async generateRecordsForPlan(planId) {
    return await this.ensureReceiveRecords({
      planId,
      source: 'plan_change',
      forceRegenerate: true,
    });
  }

  /**
   * 定时任务使用的批量生成方法
   */
  async generateRecordsForSchedule() {
    return await this.ensureReceiveRecords({
      source: 'schedule',
    });
  }
}

module.exports = ReceiveRecordService;
