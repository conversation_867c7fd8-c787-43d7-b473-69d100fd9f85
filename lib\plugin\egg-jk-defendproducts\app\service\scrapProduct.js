const Service = require('egg').Service;
const moment = require('moment');

class ScrapProductService extends Service {
  // 获取报废用品列表
  async getList(params) {
    const { ctx } = this;
    const EnterpriseID = ctx.session.adminUserInfo ? ctx.session.adminUserInfo.EnterpriseID : '';
    const { query = {}, current, pageSize, year, isSelected } = params;
    const regexMatch = {}; // 数据查询条件
    regexMatch.$or = [
      { workshop: { $regex: query.workplace || query.searchKey } },
      { workspaces: { $regex: query.workplace || query.searchKey } },
      { workstation: { $regex: query.workplace || query.searchKey } },
      { modelNumber: { $regex: query.searchKey } },
      { productName: { $regex: query.productName || query.searchKey } },
      { categoryName: { $regex: query.categoryName || query.searchKey } }, // 新增分类名称搜索
      { categoryPath: { $regex: query.categoryPath || query.searchKey } }, // 新增分类路径搜索
    ];

    // 支持按分类筛选
    if (query.categoryId) {
      regexMatch.categoryId = query.categoryId;
    }

    // 支持按仓库筛选
    if (query.warehouseId) {
      regexMatch.warehouseId = query.warehouseId;
    }
    const pipeline = [
      { $match: { EnterpriseID } },
      { $match: regexMatch },
      { $match: { yearNumber: year } },
      { $match: { mouthNumber: isSelected } },
      { $sort: {
        status: 1,
      } },
      { $facet: {
        data: [
          {
            $skip: (current - 1) * Number(pageSize),
          },
          {
            $limit: Number(pageSize),
          },
          {
            $lookup: {
              from: 'employees',
              localField: 'employee',
              foreignField: '_id',
              as: 'employeeInfo',
            },
          },
          {
            $lookup: {
              from: 'warehouses',
              localField: 'warehouseId',
              foreignField: '_id',
              as: 'warehouseInfo',
            },
          },
          {
            $project: {
              workshop: 1, // 厂房名称
              workspaces: 1, // 车间名称
              workstation: 1, // 岗位名称

              // 新增分类和仓库字段
              warehouseId: 1, // 仓库ID
              warehouseName: 1, // 仓库名称（原始字段）
              warehouseInfo: { $arrayElemAt: [ '$warehouseInfo', 0 ] }, // 仓库详细信息
              categoryId: 1, // 分类ID
              categoryPath: 1, // 分类路径
              categoryName: 1, // 分类名称

              products: 1, // 产品列表
              status: 1, // 状态：0 未审核；1 已报废；2 暂定
              operator: 1, // 操作人
              employeeInfo: 1, // 申请人
              applicationTime: 1, // 申请报废时间
              yearNumber: 1, // 年份
              mouthNumber: 1, // 月份
              scrapTime: 1, // 报废时间
            },
          },
        ],
        totalLength: [{ $count: 'length' }],
      } },
    ];
    let res = await ctx.service.db.aggregate('ScrapProduct', pipeline);
    if (res[0]) {
      res = res[0];
      if (res.data.length > 0) {
        res.data.forEach(item => {
          if (item.scrapTime) {
            item.scrapTime = moment(item.scrapTime).format('YYYY-MM-DD HH:mm');
          }
        });
      }
      res.totalLength = res.totalLength[0] ? res.totalLength[0].length : 0;
    }
    return res;
  }

  // 确认报废
  async confirmScrap(params) {
    const { ctx } = this;
    const operator = ctx.session.adminUserInfo;
    const EnterpriseID = ctx.session.adminUserInfo ? ctx.session.adminUserInfo.EnterpriseID : '';
    let res;
    if (params.id) { // 单独审核
      res = await ctx.service.db.updateOne('ScrapProduct', {
        EnterpriseID,
        _id: params.id,
        status: 0,
      },
      {
        $set: {
          scrapTime: new Date(),
          status: 1,
          'operator._id': operator._id,
          'operator.name': operator.name,
        },
      });
    } else { // 批量审核
      ctx.service.db.updateMany('ScrapProduct', {
        EnterpriseID,
        mouthNumber: params.mouthNumber,
        yearNumber: params.yearNumber,
        status: 0,
      },
      {
        $set: {
          scrapTime: new Date(),
          status: 1,
          'operator._id': operator._id,
          'operator.name': operator.name,
        },
      });
    }
    console.log(res, '111');
    return '审核通过！';
  }

  // 删除报废信息
  async deleteScrapRecord(params) {
    const { ctx } = this;

    const { ids } = params;

    await ctx.service.db.deleteMany('ScrapProduct', { _id: { $in: ids } });

    return '删除成功！';
  }

  // 按照月度导出报废列表
  async exportByMonth(params) {
    const { ctx, app } = this;
    const EnterpriseID = ctx.session.adminUserInfo ? ctx.session.adminUserInfo.EnterpriseID : '';
    const { mouthNumber, yearNumber } = params;

    console.log(EnterpriseID, mouthNumber, yearNumber);

    // 查询数据库
    const pipeline = [
      {
        $match: {
          EnterpriseID,
          mouthNumber: mouthNumber * 1,
          yearNumber: yearNumber * 1,
          status: 1,
        },
      },
      {
        $lookup: {
          from: 'warehouses',
          localField: 'warehouseId',
          foreignField: '_id',
          as: 'warehouseInfo',
        },
      },
      {
        $unwind: '$products',
      },
      {
        $project: {
          products: 1,
          scrapReason: 1, // 报废原因
          workspaces: 1, // 车间名称

          // 新增分类和仓库信息
          warehouseId: 1, // 仓库ID
          warehouseName: 1, // 仓库名称（原始字段）
          warehouseInfo: { $arrayElemAt: [ '$warehouseInfo', 0 ] }, // 仓库详细信息
          categoryId: 1, // 分类ID
          categoryPath: 1, // 分类路径
          categoryName: 1, // 分类名称

          sign: 1, // 签字
          scrapTime: 1, // 报废时间
        },
      },
    ];
    const tableData = await ctx.service.db.aggregate('ScrapProduct', pipeline);

    if (tableData.length === 0) {
      return '暂无报废数据或数据未审核通过！';
    }

    for (let i = 0; i < tableData.length; i++) {
      const temp = tableData[i];
      temp.index = i + 1;
      temp.product = temp.products.product;
      temp.productNumber = temp.products.modelNumber;
      temp.productSpec = temp.products.productSpec || ''; // 产品规格
      temp.materialCode = temp.products.materialCode || ''; // 物料编码
      temp.number = temp.products.number;

      // 新增分类信息
      temp.categoryName = temp.categoryName || temp.products.categoryName || ''; // 分类名称
      temp.categoryPath = temp.categoryPath || temp.products.categoryPath || ''; // 分类路径
      temp.warehouseName = temp.warehouseName || ''; // 仓库名称

      // delete temp.products;
      temp.scrapTime = moment(temp.scrapTime).format('YYYY-MM-DD HH:mm');
      if (temp.sign) {
        temp.sign = `${app.config.upload_path}/${temp.sign}`;
      }
    }


    console.log(tableData, '321312312312');

    const templateWordName = '特种劳动防护用品报废登记表';
    const word = await ctx.helper.fillWord(ctx, templateWordName, { tableData, projectSN: '111' });
    return word.path;
  }

}

module.exports = ScrapProductService;
