/**
 * 测试导入数据转换逻辑
 * 验证validData格式是否能正确转换为saveSingle格式
 */

// 模拟从验证接口返回的validData格式
const mockValidData = [
  {
    "rowIndex": 2,
    "workTypePath": "PVC装置/PVC装置乙炔压滤/压滤机操作工",
    "originalWorkTypePath": "PVC装置/PVC装置乙炔压滤/压滤机操作工",
    "enterpriseName": null,
    "categoryPath": "眼面部防护/防护眼镜",
    "quantity": 1,
    "cycle": 6,
    "cycleUnit": "月",
    "remark": "",
    "workTypeInfo": {
      "employees": ["0001T31000000000MMSV"],
      "totalEmployeeCount": 1,
      "state": "1",
      "_id": "FsqsCYzbl",
      "EnterpriseID": "0001T21000000000E1MJ",
      "enterpriseName": "集团本部",
      "parentId": "f4fyMvsERP",
      "name": "压滤机操作工",
      "level": "stations",
      "fullId": "rWPOGkCRwe_child_workspaces_f4fyMvsERP_child_stations_FsqsCYzbl",
      "category": "stations",
      "millName": "PVC装置",
      "workspaceName": "PVC装置乙炔压滤",
      "stationName": "压滤机操作工",
      "fullPath": "PVC装置/PVC装置乙炔压滤/压滤机操作工"
    },
    "categoryInfo": {
      "_doc": {
        "defaultCycle": {
          "timeUnit": "M",
          "time": 6
        },
        "level": 2,
        "parentId": "J5uTfGhkrg",
        "harmFactors": ["飞溅颗粒", "化学液体"],
        "isLeaf": true,
        "isActive": true,
        "sort": 1,
        "isSystemDefault": false,
        "_id": "JPVzQba3kL",
        "name": "防护眼镜",
        "topEnterpriseId": "0001T21000000000E1MJ",
        "code": "YM001",
        "description": "化工车间专用防护眼镜",
        "attributes": [],
        "path": "眼面部防护/防护眼镜"
      },
      "name": "防护眼镜"
    },
    "protectionProductName": "防护眼镜"
  },
  {
    "rowIndex": 3,
    "workTypePath": "PVC装置/PVC装置乙炔压滤/压滤机操作工",
    "originalWorkTypePath": "PVC装置/PVC装置乙炔压滤/压滤机操作工",
    "enterpriseName": null,
    "categoryPath": "听力防护/耳塞",
    "quantity": 2,
    "cycle": 1,
    "cycleUnit": "月",
    "remark": "",
    "workTypeInfo": {
      "employees": ["0001T31000000000MMSV"],
      "totalEmployeeCount": 1,
      "state": "1",
      "_id": "FsqsCYzbl",
      "EnterpriseID": "0001T21000000000E1MJ",
      "enterpriseName": "集团本部",
      "parentId": "f4fyMvsERP",
      "name": "压滤机操作工",
      "level": "stations",
      "fullId": "rWPOGkCRwe_child_workspaces_f4fyMvsERP_child_stations_FsqsCYzbl",
      "category": "stations",
      "millName": "PVC装置",
      "workspaceName": "PVC装置乙炔压滤",
      "stationName": "压滤机操作工",
      "fullPath": "PVC装置/PVC装置乙炔压滤/压滤机操作工"
    },
    "categoryInfo": {
      "_doc": {
        "defaultCycle": {
          "timeUnit": "M",
          "time": 1
        },
        "level": 2,
        "parentId": "2iqVivMu4X",
        "harmFactors": ["噪声"],
        "isLeaf": true,
        "isActive": true,
        "sort": 1,
        "isSystemDefault": false,
        "_id": "k1nttjg9d5",
        "name": "耳塞",
        "topEnterpriseId": "0001T21000000000E1MJ",
        "code": "TL001",
        "description": "一次性耳塞",
        "attributes": [],
        "path": "听力防护/耳塞"
      },
      "name": "耳塞"
    },
    "protectionProductName": "耳塞"
  }
];

// 期望的saveSingle格式
const expectedSaveSingleFormat = {
  "nodeFullId": "rWPOGkCRwe_child_workspaces_f4fyMvsERP_child_stations_FsqsCYzbl",
  "nodeLevel": "stations",
  "nodeName": "压滤机操作工",
  "products": [
    {
      "_id": "generated_id",
      "product": "防护眼镜",
      "modelNumber": "",
      "productIds": [],
      "productType": ["JPVzQba3kL"],
      "categoryId": "JPVzQba3kL",
      "categoryPath": "眼面部防护/防护眼镜",
      "categoryName": "防护眼镜",
      "number": 1,
      "time": 6,
      "timeUnit": "M",
      "maxNumber": "",
      "remark": ""
    },
    {
      "_id": "generated_id",
      "product": "耳塞",
      "modelNumber": "",
      "productIds": [],
      "productType": ["k1nttjg9d5"],
      "categoryId": "k1nttjg9d5",
      "categoryPath": "听力防护/耳塞",
      "categoryName": "耳塞",
      "number": 2,
      "time": 1,
      "timeUnit": "M",
      "maxNumber": "",
      "remark": ""
    }
  ],
  "configStatus": "configured",
  "category": "stations",
  "subRegion": []
};

// 模拟转换函数
function convertStandardToProduct(standard) {
  const shortid = require('shortid');
  
  // 从categoryInfo中提取分类信息
  const categoryInfo = standard.categoryInfo;
  let categoryId = '';
  let categoryPath = '';
  let categoryName = '';

  // 处理categoryInfo可能是Mongoose文档的情况
  if (categoryInfo) {
    if (categoryInfo._doc) {
      // Mongoose文档格式
      categoryId = categoryInfo._doc._id || categoryInfo._id;
      categoryPath = categoryInfo._doc.path || categoryInfo.path || '';
      categoryName = categoryInfo._doc.name || categoryInfo.name || '';
    } else {
      // 普通对象格式
      categoryId = categoryInfo._id;
      categoryPath = categoryInfo.path || '';
      categoryName = categoryInfo.name || '';
    }
  }

  // 清理路径，去掉开头的斜线
  const cleanPath = categoryPath ? categoryPath.replace(/^\//, '') : '';

  return {
    _id: shortid.generate(),
    product: categoryName,
    modelNumber: '',
    productIds: [],
    productType: [categoryId],
    categoryId: categoryId,
    categoryPath: cleanPath,
    categoryName: categoryName,
    number: standard.quantity,
    time: standard.cycle,
    timeUnit: convertCycleUnitToEnglish(standard.cycleUnit),
    maxNumber: '',
    remark: standard.remark || '',
  };
}

function convertCycleUnitToEnglish(chineseUnit) {
  const unitMap = {
    '天': 'D',
    '周': 'W',
    '月': 'M',
    '季': 'Q',
    '年': 'Y',
  };
  return unitMap[chineseUnit] || 'M';
}

// 按工种分组数据
function groupDataByWorkType(data) {
  const grouped = {};
  data.forEach(item => {
    const workTypeFullId = item.workTypeInfo.fullId;
    if (!grouped[workTypeFullId]) {
      grouped[workTypeFullId] = [];
    }
    grouped[workTypeFullId].push(item);
  });
  return grouped;
}

console.log('=== 导入数据转换测试 ===');
console.log('');

// 1. 按工种分组
const groupedData = groupDataByWorkType(mockValidData);
console.log('1. 分组结果:');
Object.keys(groupedData).forEach(workTypeFullId => {
  console.log(`  工种: ${workTypeFullId}, 产品数量: ${groupedData[workTypeFullId].length}`);
});
console.log('');

// 2. 转换每个工种的数据
for (const [workTypeFullId, standards] of Object.entries(groupedData)) {
  console.log(`2. 处理工种: ${workTypeFullId}`);
  
  // 构建产品数据
  const products = standards.map(standard => convertStandardToProduct(standard));
  
  // 从第一个标准中获取工种信息
  const workTypeInfo = standards[0].workTypeInfo;
  
  // 构建saveSingle数据结构
  const saveSingleData = {
    nodeFullId: workTypeFullId,
    nodeLevel: workTypeInfo.level || 'stations',
    nodeName: workTypeInfo.stationName || workTypeInfo.name,
    products,
    configStatus: 'configured',
    category: workTypeInfo.level || 'stations',
    subRegion: [],
  };
  
  console.log('转换后的saveSingle数据:');
  console.log(JSON.stringify(saveSingleData, null, 2));
  console.log('');
  
  // 验证关键字段
  console.log('3. 验证结果:');
  console.log(`  nodeFullId: ${saveSingleData.nodeFullId === expectedSaveSingleFormat.nodeFullId ? '✓' : '✗'}`);
  console.log(`  nodeLevel: ${saveSingleData.nodeLevel === expectedSaveSingleFormat.nodeLevel ? '✓' : '✗'}`);
  console.log(`  nodeName: ${saveSingleData.nodeName === expectedSaveSingleFormat.nodeName ? '✓' : '✗'}`);
  console.log(`  products数量: ${saveSingleData.products.length === expectedSaveSingleFormat.products.length ? '✓' : '✗'}`);
  console.log(`  configStatus: ${saveSingleData.configStatus === expectedSaveSingleFormat.configStatus ? '✓' : '✗'}`);
  console.log(`  category: ${saveSingleData.category === expectedSaveSingleFormat.category ? '✓' : '✗'}`);
  
  // 验证产品字段
  saveSingleData.products.forEach((product, index) => {
    const expected = expectedSaveSingleFormat.products[index];
    console.log(`  产品${index + 1}:`);
    console.log(`    product: ${product.product === expected.product ? '✓' : '✗'} (${product.product})`);
    console.log(`    productType: ${JSON.stringify(product.productType) === JSON.stringify(expected.productType) ? '✓' : '✗'}`);
    console.log(`    categoryId: ${product.categoryId === expected.categoryId ? '✓' : '✗'}`);
    console.log(`    number: ${product.number === expected.number ? '✓' : '✗'}`);
    console.log(`    time: ${product.time === expected.time ? '✓' : '✗'}`);
    console.log(`    timeUnit: ${product.timeUnit === expected.timeUnit ? '✓' : '✗'}`);
  });
}

console.log('');
console.log('✓ 数据转换逻辑测试完成！');
