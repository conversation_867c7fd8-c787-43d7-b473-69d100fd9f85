/**
 * 测试防护用品配发标准Excel导入功能
 * 用于验证合并单元格和数据解析的修复
 */

const XLSX = require('xlsx');

// 模拟修复后的合并单元格处理方法
function fillMergedCells(worksheet) {
  if (!worksheet['!merges']) {
    return;
  }

  console.log('处理合并单元格数量:', worksheet['!merges'].length);

  for (const mRange of worksheet['!merges']) {
    // 获取合并区域的起始单元格地址和值
    const startCell = XLSX.utils.encode_cell(mRange.s);
    const startValue = worksheet[startCell];

    console.log(`合并区域: ${XLSX.utils.encode_range(mRange)}, 起始单元格: ${startCell}, 值:`, startValue);

    // 如果起始单元格有值，则填充到合并范围内的所有单元格
    if (startValue && startValue.v !== undefined) {
      // 填充合并范围内的所有单元格
      for (let r = mRange.s.r; r <= mRange.e.r; r++) {
        for (let c = mRange.s.c; c <= mRange.e.c; c++) {
          const cellAddress = XLSX.utils.encode_cell({ c, r });
          // 只有当目标单元格为空或不存在时才填充
          if (!worksheet[cellAddress] || worksheet[cellAddress].v === undefined || worksheet[cellAddress].v === '') {
            worksheet[cellAddress] = {
              ...startValue,
              v: startValue.v,
              w: startValue.w || String(startValue.v),
              t: startValue.t || 's',
            };
            console.log(`填充单元格 ${cellAddress} 为:`, worksheet[cellAddress].v);
          }
        }
      }
    }
  }

  // 清理合并信息
  delete worksheet['!merges'];
}

// 模拟改进后的数字解析方法
function parseNumber(value) {
  // 处理空值或undefined
  if (value === null || value === undefined || value === '') {
    return 0;
  }

  // 转换为字符串并去除空格
  const strValue = String(value).trim();

  // 如果是空字符串，返回0
  if (strValue === '') {
    return 0;
  }

  // 尝试解析为数字
  const num = parseFloat(strValue);

  // 如果解析失败或不是有限数字，返回0
  if (isNaN(num) || !isFinite(num)) {
    return 0;
  }

  // 返回整数部分
  return Math.floor(Math.abs(num));
}

// 测试函数
function testExcelParsing(filePath) {
  try {
    console.log('开始测试Excel解析...');
    console.log('文件路径:', filePath);

    // 读取Excel文件
    const workbook = XLSX.read(filePath, {
      type: 'file',
      cellDates: true, // 自动转换日期
      cellNF: false, // 不保留数字格式
      cellText: false, // 不强制文本格式
      raw: false, // 不使用原始值
    });

    const firstSheetName = workbook.SheetNames[0];
    console.log('工作表名称:', firstSheetName);

    const worksheet = workbook.Sheets[firstSheetName];
    console.log('工作表范围:', worksheet['!ref']);

    // 处理合并单元格
    fillMergedCells(worksheet);

    // 转换为JSON数据
    const rawData = XLSX.utils.sheet_to_json(worksheet, {
      defval: '', // 空单元格默认值
      raw: false, // 不使用原始值，使用格式化后的值
      dateNF: 'yyyy-mm-dd', // 日期格式
    });

    console.log('原始数据行数:', rawData.length);
    console.log('前5行数据:');
    rawData.slice(0, 5).forEach((row, index) => {
      console.log(`第${index + 1}行:`, JSON.stringify(row, null, 2));
    });

    // 解析数据
    const parsedData = [];
    rawData.forEach((row, index) => {
      // 获取关键字段值
      const workTypePath = String(row['工种路径'] || '').trim();
      const categoryPath = String(row['防护用品分类'] || '').trim();

      // 如果关键字段都为空，跳过该行
      if (!workTypePath && !categoryPath) {
        console.log(`跳过关键字段为空的行: ${index + 1}`);
        return;
      }

      const standardItem = {
        rowIndex: index + 1,
        workTypePath,
        categoryPath,
        quantity: parseNumber(row['数量']),
        cycle: parseNumber(row['周期']),
        cycleUnit: String(row['单位'] || '').trim(),
        remark: String(row['备注'] || '').trim(),
      };

      parsedData.push(standardItem);
    });

    console.log('\n解析结果:');
    console.log('有效数据行数:', parsedData.length);
    console.log('解析后的数据:');
    parsedData.forEach((item, index) => {
      console.log(`数据项${index + 1}:`, JSON.stringify(item, null, 2));
    });

    return {
      success: true,
      data: parsedData,
      total: parsedData.length,
    };

  } catch (error) {
    console.error('测试失败:', error);
    return {
      success: false,
      error: error.message,
    };
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  const testFilePath = process.argv[2];
  if (!testFilePath) {
    console.log('用法: node test_excel_import.js <Excel文件路径>');
    process.exit(1);
  }

  const result = testExcelParsing(testFilePath);
  console.log('\n最终结果:', result);
}

module.exports = { testExcelParsing, fillMergedCells, parseNumber };
